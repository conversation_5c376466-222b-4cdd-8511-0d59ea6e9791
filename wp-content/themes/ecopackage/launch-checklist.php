<?php
/**
 * Launch Checklist - Final testing and verification
 * Access via: yoursite.com/wp-content/themes/ecopackage/launch-checklist.php
 */

// Include WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

echo "<h1>EcoPackage Website Launch Checklist</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
.check-item { margin: 10px 0; padding: 10px; border-left: 4px solid #ddd; }
.check-pass { border-left-color: #4CAF50; background: #f0f8f0; }
.check-fail { border-left-color: #f44336; background: #fff0f0; }
.check-warning { border-left-color: #ff9800; background: #fff8f0; }
h2 { color: #2D5016; margin-top: 30px; }
</style>";

$checks = array();

// 1. Theme and Basic Setup
echo "<h2>1. Theme and Basic Setup</h2>";

// Check if theme is active
$current_theme = wp_get_theme();
if ($current_theme->get('Name') === 'EcoPackage') {
    echo "<div class='check-item check-pass'>✅ EcoPackage theme is active</div>";
    $checks['theme'] = true;
} else {
    echo "<div class='check-item check-fail'>❌ EcoPackage theme is not active (Current: {$current_theme->get('Name')})</div>";
    $checks['theme'] = false;
}

// Check WooCommerce
if (class_exists('WooCommerce')) {
    echo "<div class='check-item check-pass'>✅ WooCommerce is active</div>";
    $checks['woocommerce'] = true;
} else {
    echo "<div class='check-item check-fail'>❌ WooCommerce is not active</div>";
    $checks['woocommerce'] = false;
}

// 2. Products
echo "<h2>2. Products</h2>";

$products = wc_get_products(array('limit' => -1));
if (count($products) >= 5) {
    echo "<div class='check-item check-pass'>✅ Products created (" . count($products) . " products)</div>";
    $checks['products'] = true;
} else {
    echo "<div class='check-item check-fail'>❌ Not enough products (" . count($products) . " found, need at least 5)</div>";
    $checks['products'] = false;
}

// Check product categories
$categories = get_terms(array('taxonomy' => 'product_cat', 'hide_empty' => false));
if (count($categories) >= 4) {
    echo "<div class='check-item check-pass'>✅ Product categories created (" . count($categories) . " categories)</div>";
    $checks['categories'] = true;
} else {
    echo "<div class='check-item check-warning'>⚠️ Few product categories (" . count($categories) . " found)</div>";
    $checks['categories'] = false;
}

// 3. Pages
echo "<h2>3. Essential Pages</h2>";

$required_pages = array(
    'Home' => 'Homepage',
    'Shop' => 'Shop page',
    'Contact' => 'Contact page',
    'Free Samples' => 'Samples page',
    'Privacy Policy' => 'Privacy Policy',
    'Terms & Conditions' => 'Terms & Conditions'
);

$pages_ok = true;
foreach ($required_pages as $page_title => $description) {
    $page = get_page_by_title($page_title);
    if ($page && $page->post_status === 'publish') {
        echo "<div class='check-item check-pass'>✅ {$description} exists</div>";
    } else {
        echo "<div class='check-item check-fail'>❌ {$description} missing</div>";
        $pages_ok = false;
    }
}
$checks['pages'] = $pages_ok;

// 4. SEO and Meta Tags
echo "<h2>4. SEO and Meta Tags</h2>";

// Check if meta tags function exists
if (function_exists('ecopackage_add_meta_tags')) {
    echo "<div class='check-item check-pass'>✅ SEO meta tags function exists</div>";
    $checks['seo'] = true;
} else {
    echo "<div class='check-item check-fail'>❌ SEO meta tags function missing</div>";
    $checks['seo'] = false;
}

// Check if schema markup function exists
if (function_exists('ecopackage_add_schema_markup')) {
    echo "<div class='check-item check-pass'>✅ Schema markup function exists</div>";
} else {
    echo "<div class='check-item check-fail'>❌ Schema markup function missing</div>";
}

// 5. Responsive Design
echo "<h2>5. Design and Responsiveness</h2>";

// Check if CSS file exists
$css_file = get_template_directory() . '/style.css';
if (file_exists($css_file)) {
    $css_size = filesize($css_file);
    if ($css_size > 50000) { // At least 50KB of CSS
        echo "<div class='check-item check-pass'>✅ CSS file exists and has content (" . round($css_size/1024) . "KB)</div>";
        $checks['css'] = true;
    } else {
        echo "<div class='check-item check-warning'>⚠️ CSS file seems small (" . round($css_size/1024) . "KB)</div>";
        $checks['css'] = false;
    }
} else {
    echo "<div class='check-item check-fail'>❌ CSS file missing</div>";
    $checks['css'] = false;
}

// Check if JS file exists
$js_file = get_template_directory() . '/js/main.js';
if (file_exists($js_file)) {
    echo "<div class='check-item check-pass'>✅ JavaScript file exists</div>";
} else {
    echo "<div class='check-item check-fail'>❌ JavaScript file missing</div>";
}

// 6. Forms and Functionality
echo "<h2>6. Forms and Functionality</h2>";

// Check if contact form template exists
$contact_template = get_template_directory() . '/page-contact.php';
if (file_exists($contact_template)) {
    echo "<div class='check-item check-pass'>✅ Contact form template exists</div>";
    $checks['forms'] = true;
} else {
    echo "<div class='check-item check-fail'>❌ Contact form template missing</div>";
    $checks['forms'] = false;
}

// Check if samples form template exists
$samples_template = get_template_directory() . '/page-samples.php';
if (file_exists($samples_template)) {
    echo "<div class='check-item check-pass'>✅ Samples form template exists</div>";
} else {
    echo "<div class='check-item check-fail'>❌ Samples form template missing</div>";
}

// 7. Performance and Security
echo "<h2>7. Performance and Security</h2>";

// Check if functions.php exists and has content
$functions_file = get_template_directory() . '/functions.php';
if (file_exists($functions_file)) {
    $functions_size = filesize($functions_file);
    if ($functions_size > 10000) { // At least 10KB
        echo "<div class='check-item check-pass'>✅ Functions.php exists and has content (" . round($functions_size/1024) . "KB)</div>";
    } else {
        echo "<div class='check-item check-warning'>⚠️ Functions.php seems small</div>";
    }
} else {
    echo "<div class='check-item check-fail'>❌ Functions.php missing</div>";
}

// Check WordPress version
$wp_version = get_bloginfo('version');
if (version_compare($wp_version, '6.0', '>=')) {
    echo "<div class='check-item check-pass'>✅ WordPress version is current ({$wp_version})</div>";
} else {
    echo "<div class='check-item check-warning'>⚠️ WordPress version might be outdated ({$wp_version})</div>";
}

// 8. Content and Links
echo "<h2>8. Content and Links</h2>";

// Check for placeholder content
$placeholder_check = true;
$sample_pages = array('Home', 'About Us', 'Contact');
foreach ($sample_pages as $page_title) {
    $page = get_page_by_title($page_title);
    if ($page) {
        $content = $page->post_content;
        if (strpos($content, 'Lorem ipsum') !== false || strpos($content, 'placeholder') !== false) {
            echo "<div class='check-item check-warning'>⚠️ {$page_title} page may contain placeholder content</div>";
            $placeholder_check = false;
        }
    }
}

if ($placeholder_check) {
    echo "<div class='check-item check-pass'>✅ No obvious placeholder content found</div>";
}

// 9. Final Score
echo "<h2>Launch Readiness Score</h2>";

$total_checks = count($checks);
$passed_checks = array_sum($checks);
$score = round(($passed_checks / $total_checks) * 100);

if ($score >= 90) {
    $score_class = 'check-pass';
    $score_icon = '🚀';
    $score_message = 'Excellent! Your website is ready for launch.';
} elseif ($score >= 75) {
    $score_class = 'check-warning';
    $score_icon = '⚠️';
    $score_message = 'Good! Address the remaining issues before launch.';
} else {
    $score_class = 'check-fail';
    $score_icon = '❌';
    $score_message = 'Needs work before launch. Please fix the critical issues.';
}

echo "<div class='check-item {$score_class}' style='font-size: 1.2em; font-weight: bold;'>";
echo "{$score_icon} Launch Readiness: {$score}% ({$passed_checks}/{$total_checks} checks passed)";
echo "<br><em>{$score_message}</em>";
echo "</div>";

// 10. Next Steps
echo "<h2>Next Steps</h2>";
echo "<div class='check-item'>";
echo "<h3>Before Going Live:</h3>";
echo "<ul>";
echo "<li>Test all forms (contact, samples, quotes)</li>";
echo "<li>Verify all internal links work correctly</li>";
echo "<li>Test responsive design on mobile devices</li>";
echo "<li>Set up Google Analytics and Search Console</li>";
echo "<li>Configure SSL certificate</li>";
echo "<li>Set up email notifications</li>";
echo "<li>Test payment processing (if applicable)</li>";
echo "<li>Create XML sitemap</li>";
echo "<li>Set up backup system</li>";
echo "</ul>";

echo "<h3>After Launch:</h3>";
echo "<ul>";
echo "<li>Submit sitemap to search engines</li>";
echo "<li>Monitor website performance</li>";
echo "<li>Set up monitoring for uptime</li>";
echo "<li>Create social media accounts</li>";
echo "<li>Start content marketing</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Quick Links</h2>";
echo "<p>";
echo "<a href='" . home_url() . "' target='_blank' style='margin-right: 20px;'>🏠 View Website</a>";
echo "<a href='" . home_url('/shop') . "' target='_blank' style='margin-right: 20px;'>🛒 Shop Page</a>";
echo "<a href='" . home_url('/contact') . "' target='_blank' style='margin-right: 20px;'>📞 Contact Page</a>";
echo "<a href='" . admin_url() . "' target='_blank' style='margin-right: 20px;'>⚙️ WordPress Admin</a>";
echo "</p>";

echo "<hr style='margin: 40px 0;'>";
echo "<p><em>Launch checklist completed on " . date('F j, Y \a\t g:i A') . "</em></p>";
?>
