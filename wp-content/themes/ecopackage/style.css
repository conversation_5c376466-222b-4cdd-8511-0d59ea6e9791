/*
Theme Name: EcoPackage
Description: A modern, sustainable packaging e-commerce theme inspired by noissue.co
Version: 1.0
Author: Your Company
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #F7F1EC;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    font-size: 1rem;
    line-height: 1.6;
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* Header Styles */
.site-header {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2D5016;
    text-decoration: none;
}

.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.main-nav a:hover {
    color: #2D5016;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #F7F1EC 0%, #E8DCC6 100%);
    padding: 120px 0 80px;
    text-align: center;
}

.hero-content h1 {
    font-size: 3.5rem;
    color: #2D5016;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: #2D5016;
    color: white;
}

.btn-primary:hover {
    background: #1a2f0c;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #2D5016;
    border: 2px solid #2D5016;
}

.btn-secondary:hover {
    background: #2D5016;
    color: white;
}

/* Product Categories Grid */
.categories-section {
    padding: 80px 0;
    background: white;
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title h2 {
    color: #2D5016;
    font-size: 2.5rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.category-card {
    background: #F7F1EC;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.category-image {
    height: 200px;
    background-size: cover;
    background-position: center;
}

.category-content {
    padding: 1.5rem;
}

.category-content h3 {
    color: #2D5016;
    margin-bottom: 0.5rem;
}

.category-content p {
    color: #666;
    font-size: 0.9rem;
}

/* Product Cards */
.products-section {
    padding: 80px 0;
    background: #F7F1EC;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    height: 250px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #2D5016;
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
}

.product-meta {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2D5016;
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.feature-tag {
    background: #E8DCC6;
    color: #2D5016;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Client Logos Section */
.clients-section {
    padding: 60px 0;
    background: white;
    border-top: 1px solid #E8DCC6;
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    align-items: center;
    margin-top: 2rem;
}

.client-logo {
    text-align: center;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.client-logo:hover {
    opacity: 1;
}

.client-logo img {
    max-width: 120px;
    height: auto;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.client-logo:hover img {
    filter: grayscale(0%);
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: #2D5016;
    color: white;
    text-align: center;
}

.newsletter-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.newsletter-content p {
    color: rgba(255,255,255,0.8);
    margin-bottom: 2rem;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
}

.newsletter-form button {
    background: white;
    color: #2D5016;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background: #f0f0f0;
}

/* Footer Styles */
.site-footer {
    background: #1a2f0c;
    color: white;
    padding: 60px 0 30px;
}

.footer-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-widget-area h3 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.footer-widget-area ul {
    list-style: none;
}

.footer-widget-area ul li {
    margin-bottom: 0.5rem;
}

.footer-widget-area ul li a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-widget-area ul li a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: rgba(255,255,255,0.8);
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: white;
}

.footer-legal {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-legal p {
    color: rgba(255,255,255,0.6);
    margin: 0;
}

.legal-links {
    display: flex;
    gap: 1rem;
}

.legal-links a {
    color: rgba(255,255,255,0.6);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.legal-links a:hover {
    color: white;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cart-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.cart-link:hover {
    color: #2D5016;
}

.cart-count {
    background: #2D5016;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 2px 0;
    transition: 0.3s;
}

/* Product Landing Page Styles */
.product-landing {
    padding-top: 80px;
}

.product-hero {
    background: linear-gradient(135deg, #F7F1EC 0%, #E8DCC6 100%);
    padding: 80px 0;
}

.product-hero .row {
    align-items: center;
    gap: 3rem;
}

.product-badge {
    display: inline-block;
    background: #2D5016;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.product-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.highlight-icon {
    font-size: 1.5rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 15px 40px;
    font-size: 1.1rem;
}

.product-hero-img {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* Product Details */
.product-details {
    padding: 80px 0;
    background: white;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.detail-card {
    text-align: center;
    padding: 2rem;
    background: #F7F1EC;
    border-radius: 12px;
}

.detail-icon {
    margin-bottom: 1rem;
}

.detail-card h3 {
    color: #2D5016;
    margin-bottom: 1rem;
}

/* Features Comparison */
.product-features-detailed {
    padding: 80px 0;
    background: white;
}

.comparison-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.comparison-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    background: #2D5016;
    color: white;
    font-weight: 600;
}

.comparison-header > div {
    padding: 1rem;
    text-align: center;
}

.comparison-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    border-bottom: 1px solid #eee;
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-row > div {
    padding: 1rem;
    text-align: center;
}

.feature-col {
    text-align: left;
    font-weight: 500;
}

.poly-col {
    background: #f0f8f0;
}

/* Size Options */
.size-options {
    padding: 80px 0;
    background: #F7F1EC;
}

.sizes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.size-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.size-card:hover {
    transform: translateY(-5px);
}

.size-card.popular {
    border: 3px solid #2D5016;
}

.size-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #2D5016;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.size-visual {
    font-size: 2rem;
    font-weight: 700;
    color: #2D5016;
    margin-bottom: 1rem;
    border: 2px dashed #2D5016;
    padding: 2rem;
    border-radius: 8px;
}

.size-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2D5016;
    margin-top: 1rem;
}

/* Customization Options */
.customization-options {
    padding: 80px 0;
    background: white;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.option-card {
    background: #F7F1EC;
    border-radius: 12px;
    overflow: hidden;
}

.option-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.option-card h4 {
    color: #2D5016;
    margin: 1rem 1.5rem 0.5rem;
}

.option-card ul {
    list-style: none;
    padding: 0 1.5rem 1.5rem;
}

.option-card ul li {
    padding: 0.25rem 0;
    color: #666;
}

.option-card ul li:before {
    content: "✓ ";
    color: #2D5016;
    font-weight: bold;
}

/* Pricing Calculator */
.pricing-section {
    padding: 80px 0;
    background: #F7F1EC;
}

.pricing-calculator {
    background: white;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-top: 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: start;
}

.calculator-inputs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2D5016;
}

.input-group input,
.input-group select {
    padding: 12px;
    border: 2px solid #E8DCC6;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #2D5016;
}

.pricing-result {
    background: #F7F1EC;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.price-breakdown {
    margin-bottom: 2rem;
}

.price-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
}

.price-line.total {
    border-top: 2px solid #2D5016;
    font-weight: 700;
    font-size: 1.2rem;
    color: #2D5016;
}

.delivery-info {
    margin-top: 1rem;
    color: #666;
}

.volume-discounts {
    margin-top: 2rem;
    text-align: center;
}

.discount-tiers {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.tier {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }

    .main-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .categories-grid,
    .products-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .clients-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .product-hero .row {
        flex-direction: column;
    }

    .pricing-calculator {
        grid-template-columns: 1fr;
    }

    .comparison-header,
    .comparison-row {
        grid-template-columns: 1fr;
        text-align: left;
    }

    .comparison-header .poly-col,
    .comparison-header .box-col {
        display: none;
    }

    .comparison-row .poly-col:before {
        content: "Poly Mailers: ";
        font-weight: 600;
    }

    .comparison-row .box-col:before {
        content: "Boxes: ";
        font-weight: 600;
    }
}

/* Form Styles */
.customization-section {
    padding: 80px 0;
    background: white;
}

.customization-form {
    max-width: 800px;
    margin: 0 auto;
    background: #F7F1EC;
    padding: 3rem;
    border-radius: 12px;
    margin-top: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    margin-bottom: 2rem;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2D5016;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #E8DCC6;
    border-radius: 6px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2D5016;
}

.form-group small {
    margin-top: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.form-actions {
    text-align: center;
    margin-top: 2rem;
}

.form-note {
    margin-top: 1rem;
    color: #666;
    font-size: 0.9rem;
}

/* Samples Section */
.samples-section {
    padding: 80px 0;
    background: #F7F1EC;
}

.samples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.sample-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    text-align: center;
    transition: transform 0.3s ease;
}

.sample-card:hover {
    transform: translateY(-5px);
}

.sample-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.sample-card h4 {
    color: #2D5016;
    margin: 1rem 0 0.5rem;
}

.sample-card p {
    color: #666;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
}

.sample-card .btn {
    margin-bottom: 1.5rem;
}

/* Loading States */
.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #2D5016;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover Effects */
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(45, 80, 22, 0.3);
}

.hover-effect {
    animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Scrolled Header */
.site-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* Mobile Form Styles */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .customization-form {
        padding: 2rem 1rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .btn-large {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .product-highlights {
        grid-template-columns: 1fr;
    }

    .calculator-inputs {
        grid-template-columns: 1fr;
    }
}

/* About Page Styles */
.about-page {
    padding-top: 80px;
}

.about-hero {
    background: linear-gradient(135deg, #2D5016 0%, #1a2f0c 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.about-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: white;
}

.about-hero .hero-subtitle {
    font-size: 1.3rem;
    max-width: 800px;
    margin: 0 auto;
    opacity: 0.9;
}

/* Our Story */
.our-story {
    padding: 80px 0;
    background: white;
}

.our-story .row {
    align-items: center;
    gap: 3rem;
}

.story-content h2 {
    color: #2D5016;
    margin-bottom: 2rem;
}

.story-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    color: #555;
}

.story-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
    padding: 2rem;
    background: #F7F1EC;
    border-radius: 12px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2D5016;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.story-image img {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Sustainability Commitment */
.sustainability-commitment {
    padding: 80px 0;
    background: #F7F1EC;
}

.commitment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.commitment-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    transition: transform 0.3s ease;
}

.commitment-card:hover {
    transform: translateY(-5px);
}

.commitment-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.commitment-card h3 {
    color: #2D5016;
    margin-bottom: 1rem;
}

.commitment-card p {
    color: #666;
    line-height: 1.6;
}

/* Certifications */
.certifications {
    padding: 80px 0;
    background: white;
}

.certifications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.cert-card {
    background: #F7F1EC;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.cert-logo {
    margin-bottom: 1rem;
}

.cert-logo img {
    max-width: 120px;
    height: auto;
}

.cert-card h4 {
    color: #2D5016;
    margin-bottom: 1rem;
}

.cert-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Impact Numbers */
.impact-numbers {
    padding: 80px 0;
    background: #2D5016;
    color: white;
}

.impact-numbers .section-title h2,
.impact-numbers .section-title p {
    color: white;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.impact-stat {
    text-align: center;
    padding: 2rem;
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.5rem;
}

.impact-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: white;
    margin-bottom: 0.5rem;
}

.impact-description {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
    line-height: 1.4;
}

/* Eco Alliance */
.eco-alliance {
    padding: 80px 0;
    background: white;
}

.eco-alliance .row {
    align-items: center;
    gap: 3rem;
}

.alliance-content h2 {
    color: #2D5016;
    margin-bottom: 1.5rem;
}

.alliance-content p {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.alliance-benefits {
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #F7F1EC;
    border-radius: 8px;
}

.benefit-icon {
    font-size: 1.5rem;
}

.alliance-image img {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Team Section */
.team-section {
    padding: 80px 0;
    background: #F7F1EC;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.team-member {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.5rem;
}

.team-member h4 {
    color: #2D5016;
    margin-bottom: 0.5rem;
}

.team-member .role {
    color: #2D5016;
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-member p:not(.role) {
    color: #666;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #F7F1EC 0%, #E8DCC6 100%);
    text-align: center;
}

.cta-content h2 {
    color: #2D5016;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* About Page Mobile Styles */
@media (max-width: 768px) {
    .about-hero h1 {
        font-size: 2.5rem;
    }

    .our-story .row,
    .eco-alliance .row {
        flex-direction: column;
    }

    .story-stats {
        grid-template-columns: 1fr;
    }

    .commitment-grid,
    .certifications-grid,
    .impact-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
}
