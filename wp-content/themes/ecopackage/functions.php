<?php
/**
 * EcoPackage Theme Functions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function ecopackage_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add WooCommerce support
    add_theme_support('woocommerce');
    add_theme_support('wc-product-gallery-zoom');
    add_theme_support('wc-product-gallery-lightbox');
    add_theme_support('wc-product-gallery-slider');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ecopackage'),
        'footer' => __('Footer Menu', 'ecopackage'),
    ));
}
add_action('after_setup_theme', 'ecopackage_setup');

/**
 * Enqueue Scripts and Styles
 */
function ecopackage_scripts() {
    // Enqueue theme stylesheet
    wp_enqueue_style('ecopackage-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap', array(), null);
    
    // Enqueue theme JavaScript
    wp_enqueue_script('ecopackage-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('ecopackage-script', 'ecopackage_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ecopackage_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'ecopackage_scripts');

/**
 * Register Widget Areas
 */
function ecopackage_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'ecopackage'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here.', 'ecopackage'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'ecopackage'),
        'id'            => 'footer-1',
        'description'   => __('Footer widget area 1.', 'ecopackage'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'ecopackage'),
        'id'            => 'footer-2',
        'description'   => __('Footer widget area 2.', 'ecopackage'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'ecopackage'),
        'id'            => 'footer-3',
        'description'   => __('Footer widget area 3.', 'ecopackage'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'ecopackage_widgets_init');

/**
 * Custom Post Types for Products
 */
function ecopackage_custom_post_types() {
    // Register Custom Packaging Products
    register_post_type('packaging_product', array(
        'labels' => array(
            'name' => 'Packaging Products',
            'singular_name' => 'Packaging Product',
            'add_new' => 'Add New Product',
            'add_new_item' => 'Add New Packaging Product',
            'edit_item' => 'Edit Packaging Product',
            'new_item' => 'New Packaging Product',
            'view_item' => 'View Packaging Product',
            'search_items' => 'Search Packaging Products',
            'not_found' => 'No packaging products found',
            'not_found_in_trash' => 'No packaging products found in trash'
        ),
        'public' => true,
        'has_archive' => true,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'menu_icon' => 'dashicons-products',
        'rewrite' => array('slug' => 'packaging'),
    ));
}
add_action('init', 'ecopackage_custom_post_types');

/**
 * Add Custom Meta Boxes for Packaging Products
 */
function ecopackage_add_meta_boxes() {
    add_meta_box(
        'packaging_details',
        'Packaging Details',
        'ecopackage_packaging_details_callback',
        'packaging_product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'ecopackage_add_meta_boxes');

function ecopackage_packaging_details_callback($post) {
    wp_nonce_field('ecopackage_save_meta_box_data', 'ecopackage_meta_box_nonce');
    
    $min_quantity = get_post_meta($post->ID, '_min_quantity', true);
    $delivery_time = get_post_meta($post->ID, '_delivery_time', true);
    $price_from = get_post_meta($post->ID, '_price_from', true);
    $features = get_post_meta($post->ID, '_features', true);
    $certifications = get_post_meta($post->ID, '_certifications', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="min_quantity">Minimum Quantity:</label></th>';
    echo '<td><input type="number" id="min_quantity" name="min_quantity" value="' . esc_attr($min_quantity) . '" /></td></tr>';
    
    echo '<tr><th><label for="delivery_time">Delivery Time:</label></th>';
    echo '<td><input type="text" id="delivery_time" name="delivery_time" value="' . esc_attr($delivery_time) . '" placeholder="e.g., 3-4 weeks" /></td></tr>';
    
    echo '<tr><th><label for="price_from">Price From:</label></th>';
    echo '<td><input type="text" id="price_from" name="price_from" value="' . esc_attr($price_from) . '" placeholder="e.g., $0.50" /></td></tr>';
    
    echo '<tr><th><label for="features">Features (comma separated):</label></th>';
    echo '<td><textarea id="features" name="features" rows="3" cols="50">' . esc_textarea($features) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="certifications">Certifications (comma separated):</label></th>';
    echo '<td><textarea id="certifications" name="certifications" rows="3" cols="50">' . esc_textarea($certifications) . '</textarea></td></tr>';
    echo '</table>';
}

function ecopackage_save_meta_box_data($post_id) {
    if (!isset($_POST['ecopackage_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['ecopackage_meta_box_nonce'], 'ecopackage_save_meta_box_data')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (isset($_POST['post_type']) && 'packaging_product' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }
    
    $fields = array('min_quantity', 'delivery_time', 'price_from', 'features', 'certifications');
    
    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
        }
    }
}
add_action('save_post', 'ecopackage_save_meta_box_data');

/**
 * Customize WooCommerce
 */
function ecopackage_woocommerce_support() {
    add_theme_support('woocommerce', array(
        'thumbnail_image_width' => 300,
        'single_image_width' => 600,
        'product_grid' => array(
            'default_rows' => 3,
            'min_rows' => 2,
            'max_rows' => 8,
            'default_columns' => 3,
            'min_columns' => 2,
            'max_columns' => 5,
        ),
    ));
}
add_action('after_setup_theme', 'ecopackage_woocommerce_support');

/**
 * Remove WooCommerce default styles
 */
add_filter('woocommerce_enqueue_styles', '__return_empty_array');

/**
 * Custom excerpt length
 */
function ecopackage_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'ecopackage_excerpt_length');

/**
 * Custom excerpt more
 */
function ecopackage_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'ecopackage_excerpt_more');

/**
 * SEO Meta Tags
 */
function ecopackage_add_meta_tags() {
    global $post;

    // Default meta description
    $meta_description = 'Sustainable custom packaging solutions with low minimums and fast delivery. Eco-friendly coffee bags, mailers, tissue paper, and more.';
    $meta_title = get_bloginfo('name') . ' - Sustainable Custom Packaging';
    $meta_image = get_template_directory_uri() . '/assets/images/og-image.jpg';

    // Page-specific meta
    if (is_home() || is_front_page()) {
        $meta_title = 'EcoPackage - Sustainable Custom Packaging Solutions';
        $meta_description = 'Custom packaging that\'s beautiful, functional, and kind to the planet. Coffee bags, mailers, tissue paper with low minimums and fast delivery.';
    } elseif (is_shop()) {
        $meta_title = 'Shop Sustainable Packaging - EcoPackage';
        $meta_description = 'Browse our complete collection of eco-friendly custom packaging. From coffee bags to mailers, find sustainable solutions for your business.';
    } elseif (is_product()) {
        $product = wc_get_product();
        if ($product) {
            $meta_title = $product->get_name() . ' - Custom Sustainable Packaging';
            $meta_description = wp_trim_words($product->get_short_description(), 25, '...');
            if (has_post_thumbnail()) {
                $meta_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
            }
        }
    } elseif (is_product_category()) {
        $term = get_queried_object();
        $meta_title = $term->name . ' - Sustainable Packaging Solutions';
        $meta_description = $term->description ? wp_trim_words($term->description, 25, '...') : 'Browse our ' . strtolower($term->name) . ' collection of sustainable packaging solutions.';
    } elseif (is_single() || is_page()) {
        $meta_title = get_the_title() . ' - EcoPackage';
        if (has_excerpt()) {
            $meta_description = wp_trim_words(get_the_excerpt(), 25, '...');
        } else {
            $meta_description = wp_trim_words(get_the_content(), 25, '...');
        }
        if (has_post_thumbnail()) {
            $meta_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
        }
    }

    // Output meta tags
    echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";
    echo '<meta name="robots" content="index, follow">' . "\n";
    echo '<meta name="viewport" content="width=device-width, initial-scale=1">' . "\n";

    // Open Graph tags
    echo '<meta property="og:title" content="' . esc_attr($meta_title) . '">' . "\n";
    echo '<meta property="og:description" content="' . esc_attr($meta_description) . '">' . "\n";
    echo '<meta property="og:image" content="' . esc_url($meta_image) . '">' . "\n";
    echo '<meta property="og:url" content="' . esc_url(get_permalink()) . '">' . "\n";
    echo '<meta property="og:type" content="website">' . "\n";
    echo '<meta property="og:site_name" content="' . esc_attr(get_bloginfo('name')) . '">' . "\n";

    // Twitter Card tags
    echo '<meta name="twitter:card" content="summary_large_image">' . "\n";
    echo '<meta name="twitter:title" content="' . esc_attr($meta_title) . '">' . "\n";
    echo '<meta name="twitter:description" content="' . esc_attr($meta_description) . '">' . "\n";
    echo '<meta name="twitter:image" content="' . esc_url($meta_image) . '">' . "\n";

    // Canonical URL
    echo '<link rel="canonical" href="' . esc_url(get_permalink()) . '">' . "\n";
}
add_action('wp_head', 'ecopackage_add_meta_tags');

/**
 * Add Schema.org structured data
 */
function ecopackage_add_schema_markup() {
    if (is_front_page() || is_home()) {
        // Organization schema
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Organization',
            'name' => get_bloginfo('name'),
            'url' => home_url(),
            'logo' => get_template_directory_uri() . '/assets/images/logo.png',
            'description' => 'Sustainable custom packaging solutions with low minimums and fast delivery.',
            'address' => array(
                '@type' => 'PostalAddress',
                'addressCountry' => 'US'
            ),
            'contactPoint' => array(
                '@type' => 'ContactPoint',
                'telephone' => '******-0123',
                'contactType' => 'customer service'
            ),
            'sameAs' => array(
                'https://facebook.com/ecopackage',
                'https://instagram.com/ecopackage',
                'https://twitter.com/ecopackage'
            )
        );

        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
    }

    if (is_product()) {
        global $product;
        if ($product) {
            $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
            $delivery_time = get_post_meta(get_the_ID(), '_delivery_time', true);

            // Product schema
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Product',
                'name' => $product->get_name(),
                'description' => $product->get_short_description(),
                'image' => has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'large') : '',
                'brand' => array(
                    '@type' => 'Brand',
                    'name' => get_bloginfo('name')
                ),
                'offers' => array(
                    '@type' => 'Offer',
                    'price' => $product->get_price(),
                    'priceCurrency' => 'USD',
                    'availability' => 'https://schema.org/InStock',
                    'seller' => array(
                        '@type' => 'Organization',
                        'name' => get_bloginfo('name')
                    )
                )
            );

            if ($min_qty) {
                $schema['offers']['eligibleQuantity'] = array(
                    '@type' => 'QuantitativeValue',
                    'minValue' => $min_qty
                );
            }

            echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
        }
    }
}
add_action('wp_head', 'ecopackage_add_schema_markup');

/**
 * Add breadcrumb schema
 */
function ecopackage_breadcrumb_schema() {
    if (!is_front_page()) {
        $breadcrumbs = array();
        $breadcrumbs[] = array(
            '@type' => 'ListItem',
            'position' => 1,
            'name' => 'Home',
            'item' => home_url()
        );

        $position = 2;

        if (is_shop()) {
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'name' => 'Shop',
                'item' => wc_get_page_permalink('shop')
            );
        } elseif (is_product_category()) {
            $term = get_queried_object();
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => 'Shop',
                'item' => wc_get_page_permalink('shop')
            );
            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'name' => $term->name,
                'item' => get_term_link($term)
            );
        } elseif (is_product()) {
            $product = wc_get_product();
            $categories = get_the_terms(get_the_ID(), 'product_cat');

            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position++,
                'name' => 'Shop',
                'item' => wc_get_page_permalink('shop')
            );

            if ($categories && !is_wp_error($categories)) {
                $category = $categories[0];
                $breadcrumbs[] = array(
                    '@type' => 'ListItem',
                    'position' => $position++,
                    'name' => $category->name,
                    'item' => get_term_link($category)
                );
            }

            $breadcrumbs[] = array(
                '@type' => 'ListItem',
                'position' => $position,
                'name' => get_the_title(),
                'item' => get_permalink()
            );
        }

        if (count($breadcrumbs) > 1) {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'BreadcrumbList',
                'itemListElement' => $breadcrumbs
            );

            echo '<script type="application/ld+json">' . json_encode($schema) . '</script>' . "\n";
        }
    }
}
add_action('wp_head', 'ecopackage_breadcrumb_schema');
