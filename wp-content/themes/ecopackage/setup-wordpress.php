<?php
/**
 * WordPress Setup Script - Run this once to configure the site
 * Access via: yoursite.com/wp-content/themes/ecopackage/setup-wordpress.php
 */

// Include WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('You must be an administrator to run this setup.');
}

echo "<h1>Setting up WordPress for EcoPackage...</h1>";

// 1. Create Pages
echo "<h2>Creating Pages...</h2>";

$pages = array(
    'Home' => array(
        'post_title' => 'Home',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'index.php'
    ),
    'About' => array(
        'post_title' => 'About Us',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-about.php'
    ),
    'Contact' => array(
        'post_title' => 'Contact',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-contact.php'
    ),
    'Samples' => array(
        'post_title' => 'Free Samples',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-samples.php'
    ),
    'Coffee Bags' => array(
        'post_title' => 'Custom Coffee Bags',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-coffee-bags.php'
    ),
    'Mailers' => array(
        'post_title' => 'Custom Mailers',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-mailers.php'
    ),
    'Privacy Policy' => array(
        'post_title' => 'Privacy Policy',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-privacy-policy.php'
    ),
    'Terms & Conditions' => array(
        'post_title' => 'Terms & Conditions',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-terms-conditions.php'
    )
);

$page_ids = array();

foreach ($pages as $key => $page_data) {
    // Check if page already exists
    $existing_page = get_page_by_title($page_data['post_title']);
    
    if (!$existing_page) {
        $page_id = wp_insert_post($page_data);
        if ($page_id) {
            $page_ids[$key] = $page_id;
            echo "<p>✅ Created page: {$page_data['post_title']} (ID: {$page_id})</p>";
            
            // Set page template
            if (isset($page_data['page_template'])) {
                update_post_meta($page_id, '_wp_page_template', $page_data['page_template']);
            }
        } else {
            echo "<p>❌ Failed to create page: {$page_data['post_title']}</p>";
        }
    } else {
        $page_ids[$key] = $existing_page->ID;
        echo "<p>ℹ️ Page already exists: {$page_data['post_title']} (ID: {$existing_page->ID})</p>";
    }
}

// 2. Set Homepage
if (isset($page_ids['Home'])) {
    update_option('show_on_front', 'page');
    update_option('page_on_front', $page_ids['Home']);
    echo "<p>✅ Set homepage to: Home</p>";
}

// 3. Configure WordPress Settings
echo "<h2>Configuring WordPress Settings...</h2>";

// Site settings
update_option('blogname', 'EcoPackage');
update_option('blogdescription', 'Sustainable Custom Packaging Solutions');
update_option('date_format', 'F j, Y');
update_option('time_format', 'g:i a');
update_option('start_of_week', '1'); // Monday

// Permalink structure
global $wp_rewrite;
$wp_rewrite->set_permalink_structure('/%postname%/');
$wp_rewrite->flush_rules();

echo "<p>✅ Updated site settings and permalinks</p>";

// 4. Create Navigation Menus
echo "<h2>Creating Navigation Menus...</h2>";

// Primary Menu
$primary_menu_name = 'Primary Menu';
$primary_menu_exists = wp_get_nav_menu_object($primary_menu_name);

if (!$primary_menu_exists) {
    $primary_menu_id = wp_create_nav_menu($primary_menu_name);
    
    // Add menu items
    $menu_items = array(
        array('title' => 'Home', 'url' => home_url('/'), 'menu_order' => 1),
        array('title' => 'Shop', 'url' => home_url('/shop/'), 'menu_order' => 2),
        array('title' => 'Coffee Bags', 'url' => home_url('/custom-coffee-bags/'), 'menu_order' => 3),
        array('title' => 'Mailers', 'url' => home_url('/custom-mailers/'), 'menu_order' => 4),
        array('title' => 'About', 'url' => home_url('/about-us/'), 'menu_order' => 5),
        array('title' => 'Samples', 'url' => home_url('/free-samples/'), 'menu_order' => 6),
        array('title' => 'Contact', 'url' => home_url('/contact/'), 'menu_order' => 7)
    );
    
    foreach ($menu_items as $item) {
        wp_update_nav_menu_item($primary_menu_id, 0, array(
            'menu-item-title' => $item['title'],
            'menu-item-url' => $item['url'],
            'menu-item-status' => 'publish',
            'menu-item-type' => 'custom',
            'menu-item-position' => $item['menu_order']
        ));
    }
    
    // Assign menu to location
    $locations = get_theme_mod('nav_menu_locations');
    $locations['primary'] = $primary_menu_id;
    set_theme_mod('nav_menu_locations', $locations);
    
    echo "<p>✅ Created primary navigation menu</p>";
} else {
    echo "<p>ℹ️ Primary menu already exists</p>";
}

// Footer Menu
$footer_menu_name = 'Footer Menu';
$footer_menu_exists = wp_get_nav_menu_object($footer_menu_name);

if (!$footer_menu_exists) {
    $footer_menu_id = wp_create_nav_menu($footer_menu_name);
    
    $footer_items = array(
        array('title' => 'Privacy Policy', 'url' => home_url('/privacy-policy/'), 'menu_order' => 1),
        array('title' => 'Terms & Conditions', 'url' => home_url('/terms-conditions/'), 'menu_order' => 2),
        array('title' => 'Contact', 'url' => home_url('/contact/'), 'menu_order' => 3),
        array('title' => 'Samples', 'url' => home_url('/free-samples/'), 'menu_order' => 4)
    );
    
    foreach ($footer_items as $item) {
        wp_update_nav_menu_item($footer_menu_id, 0, array(
            'menu-item-title' => $item['title'],
            'menu-item-url' => $item['url'],
            'menu-item-status' => 'publish',
            'menu-item-type' => 'custom',
            'menu-item-position' => $item['menu_order']
        ));
    }
    
    $locations = get_theme_mod('nav_menu_locations');
    $locations['footer'] = $footer_menu_id;
    set_theme_mod('nav_menu_locations', $locations);
    
    echo "<p>✅ Created footer navigation menu</p>";
} else {
    echo "<p>ℹ️ Footer menu already exists</p>";
}

// 5. Configure WooCommerce
if (class_exists('WooCommerce')) {
    echo "<h2>Configuring WooCommerce...</h2>";
    
    // Set WooCommerce pages
    $shop_page = get_page_by_title('Shop');
    if ($shop_page) {
        update_option('woocommerce_shop_page_id', $shop_page->ID);
    }
    
    // Currency and settings
    update_option('woocommerce_currency', 'USD');
    update_option('woocommerce_currency_pos', 'left');
    update_option('woocommerce_price_thousand_sep', ',');
    update_option('woocommerce_price_decimal_sep', '.');
    update_option('woocommerce_price_num_decimals', 2);
    
    // Enable reviews
    update_option('woocommerce_enable_reviews', 'yes');
    update_option('woocommerce_review_rating_verification_required', 'no');
    
    echo "<p>✅ Configured WooCommerce settings</p>";
}

// 6. Set Theme Options
echo "<h2>Setting Theme Options...</h2>";

// Activate the theme
switch_theme('ecopackage');
echo "<p>✅ Activated EcoPackage theme</p>";

// Set custom logo (placeholder)
$custom_logo_id = 0; // You would upload and set a real logo here
set_theme_mod('custom_logo', $custom_logo_id);

// 7. Create Sample Content
echo "<h2>Creating Sample Content...</h2>";

// Create a sample blog post
$sample_post = array(
    'post_title' => 'Welcome to EcoPackage - Your Sustainable Packaging Partner',
    'post_content' => '<p>We\'re excited to launch EcoPackage, your one-stop solution for sustainable custom packaging. From coffee bags to mailers, we offer eco-friendly packaging solutions with low minimums and fast delivery.</p>

<h2>Why Choose Sustainable Packaging?</h2>
<p>Sustainable packaging isn\'t just good for the environment - it\'s good for your business too. Consumers are increasingly choosing brands that align with their values, and sustainable packaging shows your commitment to environmental responsibility.</p>

<h2>Our Product Range</h2>
<ul>
<li><strong>Coffee Bags:</strong> Keep your beans fresh with our custom coffee bags</li>
<li><strong>Mailers:</strong> Ship your products in style with custom poly and kraft mailers</li>
<li><strong>Tissue Paper:</strong> Add a premium touch to your retail packaging</li>
<li><strong>Food Paper:</strong> Safe, sustainable options for restaurants and cafes</li>
</ul>

<p>Ready to get started? <a href="/samples">Order free samples</a> or <a href="/contact">contact our team</a> for personalized assistance.</p>',
    'post_status' => 'publish',
    'post_type' => 'post',
    'post_category' => array(1) // Uncategorized
);

$existing_post = get_page_by_title($sample_post['post_title'], OBJECT, 'post');
if (!$existing_post) {
    $post_id = wp_insert_post($sample_post);
    if ($post_id) {
        echo "<p>✅ Created sample blog post (ID: {$post_id})</p>";
    }
} else {
    echo "<p>ℹ️ Sample blog post already exists</p>";
}

echo "<h2>Setup Complete!</h2>";
echo "<p><strong>Your EcoPackage website is now ready!</strong></p>";
echo "<p><a href='" . home_url() . "' target='_blank'>View Your Website</a> | <a href='" . admin_url() . "'>WordPress Admin</a></p>";

echo "<h3>Next Steps:</h3>";
echo "<ul>";
echo "<li>Upload your company logo in Appearance > Customize</li>";
echo "<li>Add real product images to replace placeholders</li>";
echo "<li>Configure payment methods in WooCommerce</li>";
echo "<li>Set up email templates and notifications</li>";
echo "<li>Add Google Analytics tracking code</li>";
echo "<li>Test all forms and functionality</li>";
echo "</ul>";
?>
