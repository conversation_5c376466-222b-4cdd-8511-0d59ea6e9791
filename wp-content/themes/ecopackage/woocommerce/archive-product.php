<?php
/**
 * The Template for displaying product archives, including the main shop page
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="shop-page">
    <!-- Shop Header -->
    <section class="shop-header">
        <div class="container">
            <div class="shop-header-content">
                <h1 class="shop-title">
                    <?php if (is_shop()) : ?>
                        Sustainable Packaging Solutions
                    <?php else : ?>
                        <?php woocommerce_page_title(); ?>
                    <?php endif; ?>
                </h1>
                <p class="shop-subtitle">
                    <?php if (is_shop()) : ?>
                        Custom packaging that's beautiful, functional, and kind to the planet. Low minimums, fast delivery.
                    <?php else : ?>
                        <?php echo category_description(); ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </section>

    <!-- Shop Content -->
    <section class="shop-content">
        <div class="container">
            <div class="shop-layout">
                
                <!-- Sidebar -->
                <aside class="shop-sidebar">
                    <div class="sidebar-widget">
                        <h3>Filter by Category</h3>
                        <?php
                        $product_categories = get_terms(array(
                            'taxonomy' => 'product_cat',
                            'hide_empty' => true,
                            'parent' => 0
                        ));
                        
                        if ($product_categories) :
                        ?>
                        <ul class="category-filter">
                            <li><a href="<?php echo wc_get_page_permalink('shop'); ?>" class="<?php echo is_shop() ? 'active' : ''; ?>">All Products</a></li>
                            <?php foreach ($product_categories as $category) : ?>
                            <li>
                                <a href="<?php echo get_term_link($category); ?>" class="<?php echo is_product_category($category->slug) ? 'active' : ''; ?>">
                                    <?php echo $category->name; ?>
                                    <span class="count">(<?php echo $category->count; ?>)</span>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                    </div>
                    
                    <div class="sidebar-widget">
                        <h3>Filter by Features</h3>
                        <div class="feature-filters">
                            <label><input type="checkbox" name="feature" value="recyclable"> Recyclable</label>
                            <label><input type="checkbox" name="feature" value="compostable"> Compostable</label>
                            <label><input type="checkbox" name="feature" value="fda-approved"> FDA Approved</label>
                            <label><input type="checkbox" name="feature" value="made-in-usa"> Made in USA</label>
                            <label><input type="checkbox" name="feature" value="fsc-certified"> FSC Certified</label>
                        </div>
                    </div>
                    
                    <div class="sidebar-widget">
                        <h3>Minimum Order Quantity</h3>
                        <div class="moq-filters">
                            <label><input type="radio" name="moq" value="all" checked> All</label>
                            <label><input type="radio" name="moq" value="100"> 100 units or less</label>
                            <label><input type="radio" name="moq" value="500"> 500 units or less</label>
                            <label><input type="radio" name="moq" value="1000"> 1000 units or less</label>
                        </div>
                    </div>
                    
                    <div class="sidebar-widget help-widget">
                        <h3>Need Help?</h3>
                        <p>Not sure which product is right for you?</p>
                        <a href="/contact" class="btn btn-secondary btn-small">Get Expert Advice</a>
                        <a href="/samples" class="btn btn-outline btn-small">Order Samples</a>
                    </div>
                </aside>

                <!-- Main Content -->
                <main class="shop-main">
                    
                    <?php if (have_posts()) : ?>
                    
                    <!-- Shop Toolbar -->
                    <div class="shop-toolbar">
                        <div class="toolbar-left">
                            <p class="woocommerce-result-count">
                                <?php woocommerce_result_count(); ?>
                            </p>
                        </div>
                        <div class="toolbar-right">
                            <?php woocommerce_catalog_ordering(); ?>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="products-grid">
                        <?php
                        woocommerce_product_loop_start();
                        
                        while (have_posts()) :
                            the_post();
                            
                            global $product;
                            ?>
                            <div class="product-card">
                                <div class="product-image">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php 
                                        if (has_post_thumbnail()) {
                                            the_post_thumbnail('woocommerce_thumbnail');
                                        } else {
                                            echo '<img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=250&fit=crop" alt="' . get_the_title() . '">';
                                        }
                                        ?>
                                    </a>
                                    
                                    <?php
                                    // Product badges
                                    $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
                                    $features = get_post_meta(get_the_ID(), '_features', true);
                                    
                                    if (strpos($features, 'Made in USA') !== false) {
                                        echo '<div class="product-badge">Made in USA</div>';
                                    } elseif ($min_qty && $min_qty <= 100) {
                                        echo '<div class="product-badge">Low MOQ</div>';
                                    }
                                    ?>
                                </div>
                                
                                <div class="product-info">
                                    <div class="product-meta">
                                        <?php
                                        $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
                                        $delivery = get_post_meta(get_the_ID(), '_delivery_time', true);
                                        
                                        if ($min_qty || $delivery) {
                                            echo '<span>';
                                            if ($min_qty) echo 'Min. ' . $min_qty . ' units';
                                            if ($min_qty && $delivery) echo ' • ';
                                            if ($delivery) echo 'Delivery: ' . $delivery;
                                            echo '</span>';
                                        }
                                        ?>
                                    </div>
                                    
                                    <h3 class="product-title">
                                        <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                    </h3>
                                    
                                    <div class="product-price">
                                        <?php 
                                        $price_from = get_post_meta(get_the_ID(), '_price_from', true);
                                        if ($price_from) {
                                            echo 'from ' . $price_from . ' per unit';
                                        } else {
                                            echo $product->get_price_html();
                                        }
                                        ?>
                                    </div>
                                    
                                    <?php
                                    $features = get_post_meta(get_the_ID(), '_features', true);
                                    if ($features) {
                                        $feature_array = explode(', ', $features);
                                        echo '<div class="product-features">';
                                        foreach (array_slice($feature_array, 0, 4) as $feature) {
                                            echo '<span class="feature-tag">' . trim($feature) . '</span>';
                                        }
                                        echo '</div>';
                                    }
                                    ?>
                                    
                                    <div class="product-actions">
                                        <a href="<?php the_permalink(); ?>" class="btn btn-primary">Customize</a>
                                        <a href="/samples?product=<?php echo urlencode(get_the_title()); ?>" class="btn btn-outline">Sample</a>
                                    </div>
                                </div>
                            </div>
                            <?php
                        endwhile;
                        
                        woocommerce_product_loop_end();
                        ?>
                    </div>

                    <?php
                    // Pagination
                    woocommerce_pagination();
                    
                    else :
                        woocommerce_output_all_notices();
                        echo '<p class="no-products">No products found matching your criteria.</p>';
                    endif;
                    ?>
                    
                </main>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose-us">
        <div class="container">
            <div class="section-title">
                <h2>Why Choose EcoPackage?</h2>
            </div>
            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">📦</div>
                    <h4>Low Minimums</h4>
                    <p>Starting from just 25 units on select products</p>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">⚡</div>
                    <h4>Fast Delivery</h4>
                    <p>Most products ship within 1-3 weeks</p>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🌱</div>
                    <h4>100% Sustainable</h4>
                    <p>Every product is eco-friendly and responsibly sourced</p>
                </div>
                <div class="benefit-item">
                    <div class="benefit-icon">🎨</div>
                    <h4>Free Design Help</h4>
                    <p>Our design team helps bring your vision to life</p>
                </div>
            </div>
        </div>
    </section>
</div>

<?php get_footer('shop'); ?>
