<?php
/**
 * The Template for displaying all single products
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="single-product-page">
    <?php while (have_posts()) : the_post(); ?>
        
        <?php global $product; ?>
        
        <!-- Product Header -->
        <section class="product-header">
            <div class="container">
                <div class="breadcrumb">
                    <?php woocommerce_breadcrumb(); ?>
                </div>
                
                <div class="product-main">
                    <div class="product-images">
                        <?php
                        if (has_post_thumbnail()) {
                            the_post_thumbnail('woocommerce_single', array('class' => 'main-product-image'));
                        } else {
                            echo '<img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=600&h=600&fit=crop" alt="' . get_the_title() . '" class="main-product-image">';
                        }
                        ?>
                        
                        <!-- Product Gallery would go here -->
                        <div class="product-gallery">
                            <div class="gallery-thumb active">
                                <?php
                                if (has_post_thumbnail()) {
                                    the_post_thumbnail('woocommerce_gallery_thumbnail');
                                } else {
                                    echo '<img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=100&h=100&fit=crop" alt="Thumbnail">';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-summary">
                        <?php
                        // Product badges
                        $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
                        $features = get_post_meta(get_the_ID(), '_features', true);
                        
                        if (strpos($features, 'Made in USA') !== false) {
                            echo '<div class="product-badge">Made in USA</div>';
                        } elseif (strpos($features, 'New') !== false) {
                            echo '<div class="product-badge">New Product</div>';
                        }
                        ?>
                        
                        <h1 class="product-title"><?php the_title(); ?></h1>
                        
                        <div class="product-meta-info">
                            <?php
                            $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
                            $delivery = get_post_meta(get_the_ID(), '_delivery_time', true);
                            
                            if ($min_qty || $delivery) {
                                echo '<div class="meta-item">';
                                if ($min_qty) echo '<span><strong>Minimum:</strong> ' . $min_qty . ' units</span>';
                                if ($delivery) echo '<span><strong>Delivery:</strong> ' . $delivery . '</span>';
                                echo '</div>';
                            }
                            ?>
                        </div>
                        
                        <div class="product-price">
                            <?php 
                            $price_from = get_post_meta(get_the_ID(), '_price_from', true);
                            if ($price_from) {
                                echo '<span class="price-from">from ' . $price_from . ' per unit</span>';
                            } else {
                                echo $product->get_price_html();
                            }
                            ?>
                        </div>
                        
                        <div class="product-short-description">
                            <?php echo $product->get_short_description(); ?>
                        </div>
                        
                        <?php
                        $features = get_post_meta(get_the_ID(), '_features', true);
                        if ($features) {
                            $feature_array = explode(', ', $features);
                            echo '<div class="product-features">';
                            echo '<h4>Key Features:</h4>';
                            echo '<div class="features-list">';
                            foreach ($feature_array as $feature) {
                                echo '<span class="feature-tag">' . trim($feature) . '</span>';
                            }
                            echo '</div>';
                            echo '</div>';
                        }
                        ?>
                        
                        <!-- Custom Quote Form -->
                        <div class="product-quote-form">
                            <h4>Get Custom Quote</h4>
                            <form class="quote-form" method="post">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="quote-quantity">Quantity *</label>
                                        <select id="quote-quantity" name="quantity" required>
                                            <option value="">Select quantity</option>
                                            <?php
                                            $min_qty = get_post_meta(get_the_ID(), '_min_quantity', true);
                                            $min_qty = $min_qty ? intval($min_qty) : 100;
                                            
                                            $quantities = array($min_qty, $min_qty * 2, $min_qty * 5, $min_qty * 10);
                                            foreach ($quantities as $qty) {
                                                echo '<option value="' . $qty . '">' . number_format($qty) . ' units</option>';
                                            }
                                            ?>
                                            <option value="custom">Custom quantity</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="quote-timeline">When do you need this?</label>
                                        <select id="quote-timeline" name="timeline">
                                            <option value="standard">Standard timeline</option>
                                            <option value="rush">Rush order (+25%)</option>
                                            <option value="flexible">I'm flexible</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="quote-email">Email Address *</label>
                                    <input type="email" id="quote-email" name="email" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="quote-message">Special Requirements</label>
                                    <textarea id="quote-message" name="message" rows="3" placeholder="Tell us about your project, design needs, or any special requirements..."></textarea>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary btn-large">Get Quote</button>
                                    <a href="/samples?product=<?php echo urlencode(get_the_title()); ?>" class="btn btn-secondary">Order Sample</a>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Trust Signals -->
                        <div class="trust-signals">
                            <div class="trust-item">
                                <span class="trust-icon">🚚</span>
                                <span>Free shipping on orders over $500</span>
                            </div>
                            <div class="trust-item">
                                <span class="trust-icon">🔒</span>
                                <span>Secure payment processing</span>
                            </div>
                            <div class="trust-item">
                                <span class="trust-icon">💬</span>
                                <span>Expert design assistance included</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Product Details Tabs -->
        <section class="product-details">
            <div class="container">
                <div class="product-tabs">
                    <div class="tab-nav">
                        <button class="tab-button active" data-tab="description">Description</button>
                        <button class="tab-button" data-tab="specifications">Specifications</button>
                        <button class="tab-button" data-tab="sustainability">Sustainability</button>
                        <button class="tab-button" data-tab="samples">Samples</button>
                    </div>
                    
                    <div class="tab-content">
                        <div class="tab-pane active" id="description">
                            <?php the_content(); ?>
                        </div>
                        
                        <div class="tab-pane" id="specifications">
                            <h3>Product Specifications</h3>
                            <?php
                            // Display product attributes
                            $attributes = $product->get_attributes();
                            if ($attributes) {
                                echo '<table class="specifications-table">';
                                foreach ($attributes as $attribute) {
                                    $name = wc_attribute_label($attribute->get_name());
                                    $values = wc_get_product_terms($product->get_id(), $attribute->get_name(), array('fields' => 'names'));
                                    echo '<tr>';
                                    echo '<td><strong>' . $name . '</strong></td>';
                                    echo '<td>' . implode(', ', $values) . '</td>';
                                    echo '</tr>';
                                }
                                echo '</table>';
                            }
                            ?>
                        </div>
                        
                        <div class="tab-pane" id="sustainability">
                            <h3>Environmental Impact</h3>
                            <?php
                            $certifications = get_post_meta(get_the_ID(), '_certifications', true);
                            if ($certifications) {
                                echo '<div class="certifications">';
                                echo '<h4>Certifications:</h4>';
                                $cert_array = explode(', ', $certifications);
                                foreach ($cert_array as $cert) {
                                    echo '<span class="cert-badge">' . trim($cert) . '</span>';
                                }
                                echo '</div>';
                            }
                            ?>
                            
                            <div class="sustainability-info">
                                <div class="sustain-item">
                                    <h4>♻️ Recyclability</h4>
                                    <p>This product can be recycled through standard municipal recycling programs.</p>
                                </div>
                                <div class="sustain-item">
                                    <h4>🌱 Compostability</h4>
                                    <p>Made from compostable materials that break down naturally in commercial composting facilities.</p>
                                </div>
                                <div class="sustain-item">
                                    <h4>🌍 Carbon Footprint</h4>
                                    <p>All shipments are carbon neutral through verified offset programs.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-pane" id="samples">
                            <h3>Order Samples</h3>
                            <p>Get a feel for the quality before you order. We offer free samples of all our products.</p>
                            
                            <div class="sample-options">
                                <div class="sample-option">
                                    <h4>Single Sample</h4>
                                    <p>One sample of this specific product</p>
                                    <a href="/samples?product=<?php echo urlencode(get_the_title()); ?>&type=single" class="btn btn-secondary">Request Sample</a>
                                </div>
                                <div class="sample-option">
                                    <h4>Sample Pack</h4>
                                    <p>Multiple variations and sizes</p>
                                    <a href="/samples?product=<?php echo urlencode(get_the_title()); ?>&type=pack" class="btn btn-secondary">Request Pack</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Related Products -->
        <section class="related-products">
            <div class="container">
                <h2>You Might Also Like</h2>
                <div class="related-products-grid">
                    <?php
                    // Get related products
                    $related_ids = wc_get_related_products($product->get_id(), 4);
                    if ($related_ids) {
                        foreach ($related_ids as $related_id) {
                            $related_product = wc_get_product($related_id);
                            if ($related_product) {
                                ?>
                                <div class="related-product-card">
                                    <a href="<?php echo get_permalink($related_id); ?>">
                                        <?php echo $related_product->get_image('woocommerce_thumbnail'); ?>
                                        <h4><?php echo $related_product->get_name(); ?></h4>
                                        <p class="price"><?php echo $related_product->get_price_html(); ?></p>
                                    </a>
                                </div>
                                <?php
                            }
                        }
                    }
                    ?>
                </div>
            </div>
        </section>
        
    <?php endwhile; ?>
</div>

<?php get_footer('shop'); ?>
