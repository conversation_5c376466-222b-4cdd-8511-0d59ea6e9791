<?php
/*
Template Name: Samples Page
*/

get_header(); ?>

<main id="main" class="site-main samples-page">
    
    <!-- Samples Hero -->
    <section class="samples-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Free Samples</h1>
                <p class="hero-subtitle">Feel the quality before you order. Get free samples of our sustainable packaging delivered to your door.</p>
                <div class="hero-highlights">
                    <div class="highlight-item">
                        <span class="highlight-icon">📦</span>
                        <span>100% Free Samples</span>
                    </div>
                    <div class="highlight-item">
                        <span class="highlight-icon">🚚</span>
                        <span>Free Shipping</span>
                    </div>
                    <div class="highlight-item">
                        <span class="highlight-icon">⚡</span>
                        <span>Ships in 1-2 Days</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sample Categories -->
    <section class="sample-categories">
        <div class="container">
            <div class="section-title">
                <h2>Choose Your Samples</h2>
                <p>Select from our most popular packaging categories</p>
            </div>
            
            <div class="categories-grid">
                <div class="sample-category-card" data-category="coffee">
                    <div class="category-image">
                        <img src="https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=300&h=200&fit=crop" alt="Coffee Packaging Samples">
                    </div>
                    <div class="category-content">
                        <h3>Coffee & Café</h3>
                        <p>Coffee bags, cups, sleeves, and café packaging</p>
                        <ul class="sample-includes">
                            <li>250g coffee bag with valve</li>
                            <li>500g coffee bag</li>
                            <li>Coffee cup sleeve</li>
                            <li>Food paper sample</li>
                        </ul>
                        <button class="btn btn-primary select-category" data-category="coffee">Select Samples</button>
                    </div>
                </div>
                
                <div class="sample-category-card" data-category="mailers">
                    <div class="category-image">
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" alt="Mailer Samples">
                    </div>
                    <div class="category-content">
                        <h3>Mailers & Shipping</h3>
                        <p>Poly mailers, kraft mailers, and shipping bags</p>
                        <ul class="sample-includes">
                            <li>10x13" poly mailer</li>
                            <li>Kraft mailer</li>
                            <li>Padded mailer</li>
                            <li>Bubble mailer</li>
                        </ul>
                        <button class="btn btn-primary select-category" data-category="mailers">Select Samples</button>
                    </div>
                </div>
                
                <div class="sample-category-card" data-category="retail">
                    <div class="category-image">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=200&fit=crop" alt="Retail Packaging Samples">
                    </div>
                    <div class="category-content">
                        <h3>Retail & Branding</h3>
                        <p>Tissue paper, bags, stickers, and retail packaging</p>
                        <ul class="sample-includes">
                            <li>Tissue paper sheets</li>
                            <li>Shopping bag</li>
                            <li>Sticker samples</li>
                            <li>Gift tags</li>
                        </ul>
                        <button class="btn btn-primary select-category" data-category="retail">Select Samples</button>
                    </div>
                </div>
                
                <div class="sample-category-card" data-category="food">
                    <div class="category-image">
                        <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop" alt="Food Packaging Samples">
                    </div>
                    <div class="category-content">
                        <h3>Food & Restaurant</h3>
                        <p>Food-safe paper, containers, and restaurant packaging</p>
                        <ul class="sample-includes">
                            <li>Food paper sheets</li>
                            <li>Deli paper</li>
                            <li>Takeout bag</li>
                            <li>Food container</li>
                        </ul>
                        <button class="btn btn-primary select-category" data-category="food">Select Samples</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sample Request Form -->
    <section class="sample-request-form" id="sample-form" style="display: none;">
        <div class="container">
            <div class="form-container">
                <h2>Request Your Free Samples</h2>
                <p>Fill out the form below and we'll send your samples within 1-2 business days.</p>
                
                <form class="samples-form" method="post">
                    <div class="selected-samples">
                        <h3>Selected Sample Pack: <span id="selected-category"></span></h3>
                        <div id="sample-list"></div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sample-name">Full Name *</label>
                            <input type="text" id="sample-name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="sample-email">Email Address *</label>
                            <input type="email" id="sample-email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="sample-company">Company Name *</label>
                            <input type="text" id="sample-company" name="company" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="sample-phone">Phone Number</label>
                            <input type="tel" id="sample-phone" name="phone">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="sample-address">Shipping Address *</label>
                        <textarea id="sample-address" name="address" rows="3" required placeholder="Street address, city, state, zip code"></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="sample-business-type">Business Type</label>
                        <select id="sample-business-type" name="business_type">
                            <option value="">Select your business type</option>
                            <option value="coffee-roaster">Coffee Roaster</option>
                            <option value="restaurant">Restaurant/Café</option>
                            <option value="retail">Retail Store</option>
                            <option value="ecommerce">E-commerce</option>
                            <option value="manufacturer">Manufacturer</option>
                            <option value="distributor">Distributor</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sample-timeline">When are you looking to order?</label>
                        <select id="sample-timeline" name="timeline">
                            <option value="">Select timeline</option>
                            <option value="immediately">Immediately</option>
                            <option value="1-month">Within 1 month</option>
                            <option value="3-months">Within 3 months</option>
                            <option value="6-months">Within 6 months</option>
                            <option value="researching">Just researching</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sample-quantity">Expected order quantity</label>
                        <select id="sample-quantity" name="quantity">
                            <option value="">Select quantity range</option>
                            <option value="100-500">100 - 500 units</option>
                            <option value="500-1000">500 - 1,000 units</option>
                            <option value="1000-5000">1,000 - 5,000 units</option>
                            <option value="5000-10000">5,000 - 10,000 units</option>
                            <option value="10000+">10,000+ units</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="sample-notes">Additional Notes</label>
                        <textarea id="sample-notes" name="notes" rows="3" placeholder="Tell us about your specific needs, design requirements, or any questions..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-large">Request Free Samples</button>
                        <button type="button" class="btn btn-secondary" onclick="hideSampleForm()">Choose Different Samples</button>
                    </div>
                    
                    <p class="form-note">
                        <strong>Free shipping worldwide.</strong> Samples typically arrive within 3-5 business days in the US, 7-10 days internationally.
                    </p>
                </form>
            </div>
        </div>
    </section>

    <!-- Why Order Samples -->
    <section class="why-samples">
        <div class="container">
            <div class="section-title">
                <h2>Why Order Samples?</h2>
                <p>Make informed decisions with hands-on experience</p>
            </div>
            
            <div class="benefits-grid">
                <div class="benefit-item">
                    <div class="benefit-icon">👋</div>
                    <h4>Feel the Quality</h4>
                    <p>Experience the texture, thickness, and durability of our materials firsthand.</p>
                </div>
                
                <div class="benefit-item">
                    <div class="benefit-icon">🎨</div>
                    <h4>Test Your Design</h4>
                    <p>See how your artwork will look on the actual packaging material.</p>
                </div>
                
                <div class="benefit-item">
                    <div class="benefit-icon">📏</div>
                    <h4>Check the Size</h4>
                    <p>Ensure the dimensions are perfect for your products.</p>
                </div>
                
                <div class="benefit-item">
                    <div class="benefit-icon">🌱</div>
                    <h4>Verify Sustainability</h4>
                    <p>Confirm the eco-friendly properties and certifications.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="sample-testimonials">
        <div class="container">
            <div class="section-title">
                <h2>What Our Customers Say</h2>
            </div>
            
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The samples helped us make the perfect choice for our coffee bags. The quality exceeded our expectations!"</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Sarah Johnson</strong>
                        <span>Founder, Mountain Peak Coffee</span>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"Free samples with fast shipping made our decision easy. The poly mailers are exactly what we needed."</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Mike Chen</strong>
                        <span>Operations Manager, StyleCo</span>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"Being able to test the sustainability claims with actual samples gave us confidence in our choice."</p>
                    </div>
                    <div class="testimonial-author">
                        <strong>Emily Rodriguez</strong>
                        <span>Brand Manager, EcoBeauty</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

</main>

<script>
// Sample selection functionality
const sampleCategories = {
    coffee: {
        name: 'Coffee & Café',
        items: ['250g coffee bag with valve', '500g coffee bag', 'Coffee cup sleeve', 'Food paper sample']
    },
    mailers: {
        name: 'Mailers & Shipping',
        items: ['10x13" poly mailer', 'Kraft mailer', 'Padded mailer', 'Bubble mailer']
    },
    retail: {
        name: 'Retail & Branding',
        items: ['Tissue paper sheets', 'Shopping bag', 'Sticker samples', 'Gift tags']
    },
    food: {
        name: 'Food & Restaurant',
        items: ['Food paper sheets', 'Deli paper', 'Takeout bag', 'Food container']
    }
};

function showSampleForm(category) {
    const categoryData = sampleCategories[category];
    document.getElementById('selected-category').textContent = categoryData.name;
    
    const sampleList = document.getElementById('sample-list');
    sampleList.innerHTML = '<ul>' + categoryData.items.map(item => `<li>${item}</li>`).join('') + '</ul>';
    
    document.getElementById('sample-form').style.display = 'block';
    document.getElementById('sample-form').scrollIntoView({ behavior: 'smooth' });
}

function hideSampleForm() {
    document.getElementById('sample-form').style.display = 'none';
    document.querySelector('.sample-categories').scrollIntoView({ behavior: 'smooth' });
}

document.addEventListener('DOMContentLoaded', function() {
    // Category selection
    document.querySelectorAll('.select-category').forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            showSampleForm(category);
        });
    });
    
    // Form submission
    document.querySelector('.samples-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Sending Request...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Thank you! Your free samples will be shipped within 1-2 business days. You\'ll receive a tracking number via email.');
            this.reset();
            hideSampleForm();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    });
});
</script>

<?php get_footer(); ?>
