/**
 * EcoPackage Theme JavaScript
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        
        // Mobile menu toggle
        $('.mobile-menu-toggle').on('click', function() {
            $(this).toggleClass('active');
            $('.main-nav').toggleClass('mobile-active');
        });

        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(event) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                event.preventDefault();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 80
                }, 1000);
            }
        });

        // Newsletter form submission
        $('.newsletter-form').on('submit', function(e) {
            e.preventDefault();
            var email = $(this).find('input[type="email"]').val();
            
            if (email) {
                // Here you would typically send the email to your backend
                alert('Thank you for subscribing! We\'ll be in touch soon.');
                $(this).find('input[type="email"]').val('');
            }
        });

        // Product card hover effects
        $('.product-card').hover(
            function() {
                $(this).find('.btn').addClass('hover-effect');
            },
            function() {
                $(this).find('.btn').removeClass('hover-effect');
            }
        );

        // Category card click tracking
        $('.category-card').on('click', function() {
            var categoryName = $(this).find('h3').text();
            // Track category clicks for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'category_click', {
                    'category_name': categoryName
                });
            }
        });

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Header scroll effect
        $(window).scroll(function() {
            var scroll = $(window).scrollTop();
            if (scroll >= 100) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }
        });

        // Product quantity selector
        $('.quantity-selector').each(function() {
            var $this = $(this);
            var $input = $this.find('input[type="number"]');
            var $minus = $this.find('.quantity-minus');
            var $plus = $this.find('.quantity-plus');

            $minus.on('click', function() {
                var currentVal = parseInt($input.val());
                var minVal = parseInt($input.attr('min')) || 1;
                if (currentVal > minVal) {
                    $input.val(currentVal - 1);
                }
            });

            $plus.on('click', function() {
                var currentVal = parseInt($input.val());
                var maxVal = parseInt($input.attr('max')) || 999999;
                if (currentVal < maxVal) {
                    $input.val(currentVal + 1);
                }
            });
        });

        // Custom packaging form handling
        $('.packaging-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = {
                product_type: $(this).find('[name="product_type"]').val(),
                quantity: $(this).find('[name="quantity"]').val(),
                size: $(this).find('[name="size"]').val(),
                colors: $(this).find('[name="colors"]').val(),
                design_upload: $(this).find('[name="design_upload"]')[0].files[0]
            };

            // Validate form
            if (!formData.quantity || formData.quantity < 100) {
                alert('Minimum quantity is 100 units.');
                return;
            }

            // Show loading state
            $(this).find('.btn-submit').text('Processing...').prop('disabled', true);

            // Here you would send the data to your backend
            setTimeout(function() {
                alert('Quote request submitted! We\'ll contact you within 24 hours.');
                $('.packaging-form')[0].reset();
                $('.packaging-form .btn-submit').text('Get Quote').prop('disabled', false);
            }, 2000);
        });

        // Price calculator
        function calculatePrice(quantity, basePrice, discountTiers) {
            var price = basePrice;
            
            discountTiers.forEach(function(tier) {
                if (quantity >= tier.min) {
                    price = tier.price;
                }
            });

            return price;
        }

        // Update price display when quantity changes
        $('.quantity-input').on('input', function() {
            var quantity = parseInt($(this).val());
            var basePrice = parseFloat($(this).data('base-price'));
            var discountTiers = $(this).data('discount-tiers') || [];
            
            if (quantity && basePrice) {
                var unitPrice = calculatePrice(quantity, basePrice, discountTiers);
                var totalPrice = unitPrice * quantity;
                
                $('.unit-price').text('$' + unitPrice.toFixed(2));
                $('.total-price').text('$' + totalPrice.toFixed(2));
            }
        });

        // Sustainability badge hover
        $('.feature-tag').hover(
            function() {
                var feature = $(this).text();
                var tooltip = getSustainabilityInfo(feature);
                if (tooltip) {
                    $(this).attr('title', tooltip);
                }
            }
        );

        function getSustainabilityInfo(feature) {
            var info = {
                'Recycle': 'This product can be recycled through standard recycling programs.',
                'Compostable': 'This product will break down naturally in commercial composting facilities.',
                'FSC-Certified': 'Made from responsibly sourced forest materials.',
                'Made in USA': 'Manufactured in the United States to reduce shipping emissions.',
                'Water-based Inks': 'Uses eco-friendly water-based printing inks.',
                'Soy-based Inks': 'Printed with sustainable soy-based inks.'
            };
            return info[feature] || null;
        }

        // Sample request functionality
        $('.request-sample').on('click', function(e) {
            e.preventDefault();
            var productName = $(this).closest('.product-card').find('.product-title').text();
            
            // Open sample request modal or redirect
            if (confirm('Request a free sample of ' + productName + '?')) {
                window.location.href = '/samples?product=' + encodeURIComponent(productName);
            }
        });

    });

    // Window load
    $(window).on('load', function() {
        // Hide loading spinner if present
        $('.loading-spinner').fadeOut();
        
        // Initialize any plugins that need the page to be fully loaded
        initializePlugins();
    });

    function initializePlugins() {
        // Initialize any third-party plugins here
        // Example: Swiper, AOS, etc.
    }

})(jQuery);
