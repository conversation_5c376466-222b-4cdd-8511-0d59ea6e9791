<?php
/**
 * Create missing pages
 */

// Include WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

echo "<h1>Creating Missing Pages...</h1>";

// Pages to create
$pages = array(
    array(
        'post_title' => 'Home',
        'post_name' => 'home',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'index.php'
    ),
    array(
        'post_title' => 'Contact',
        'post_name' => 'contact',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-contact.php'
    ),
    array(
        'post_title' => 'Free Samples',
        'post_name' => 'free-samples',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-samples.php'
    ),
    array(
        'post_title' => 'Privacy Policy',
        'post_name' => 'privacy-policy',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-privacy-policy.php'
    ),
    array(
        'post_title' => 'Terms & Conditions',
        'post_name' => 'terms-conditions',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-terms-conditions.php'
    ),
    array(
        'post_title' => 'About Us',
        'post_name' => 'about-us',
        'post_content' => '',
        'post_status' => 'publish',
        'post_type' => 'page',
        'page_template' => 'page-about.php'
    )
);

$created_pages = array();

foreach ($pages as $page_data) {
    // Check if page already exists
    $existing_page = get_page_by_title($page_data['post_title']);
    
    if (!$existing_page) {
        $page_id = wp_insert_post($page_data);
        if ($page_id) {
            $created_pages[] = $page_id;
            echo "<p>✅ Created page: {$page_data['post_title']} (ID: {$page_id})</p>";
            
            // Set page template
            if (isset($page_data['page_template'])) {
                update_post_meta($page_id, '_wp_page_template', $page_data['page_template']);
            }
        } else {
            echo "<p>❌ Failed to create page: {$page_data['post_title']}</p>";
        }
    } else {
        echo "<p>ℹ️ Page already exists: {$page_data['post_title']} (ID: {$existing_page->ID})</p>";
    }
}

// Set homepage
$home_page = get_page_by_title('Home');
if ($home_page) {
    update_option('show_on_front', 'page');
    update_option('page_on_front', $home_page->ID);
    echo "<p>✅ Set homepage to: Home</p>";
}

// Update site settings
update_option('blogname', 'EcoPackage');
update_option('blogdescription', 'Sustainable Custom Packaging Solutions');

echo "<h2>Pages Created Successfully!</h2>";
echo "<p>Created " . count($created_pages) . " new pages.</p>";
echo "<p><a href='" . home_url() . "'>View Website</a></p>";
?>
