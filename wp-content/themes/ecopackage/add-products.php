<?php
/**
 * Add WooCommerce Products - Run this once to populate the store
 * Access via: yoursite.com/wp-content/themes/ecopackage/add-products.php
 */

// Include WordPress
require_once(dirname(__FILE__) . '/../../../wp-load.php');

// Check if WooCommerce is active
if (!class_exists('WooCommerce')) {
    die('WooCommerce is not active!');
}

// Function to create a product
function create_packaging_product($data) {
    $product = new WC_Product_Simple();
    
    // Basic product data
    $product->set_name($data['name']);
    $product->set_description($data['description']);
    $product->set_short_description($data['short_description']);
    $product->set_regular_price($data['price']);
    $product->set_sku($data['sku']);
    $product->set_manage_stock(true);
    $product->set_stock_quantity(9999);
    $product->set_stock_status('instock');
    $product->set_catalog_visibility('visible');
    $product->set_status('publish');
    
    // Categories
    if (isset($data['categories'])) {
        $category_ids = array();
        foreach ($data['categories'] as $category_name) {
            $category = get_term_by('name', $category_name, 'product_cat');
            if (!$category) {
                $category = wp_insert_term($category_name, 'product_cat');
                $category_ids[] = $category['term_id'];
            } else {
                $category_ids[] = $category->term_id;
            }
        }
        $product->set_category_ids($category_ids);
    }
    
    // Custom attributes
    if (isset($data['attributes'])) {
        $attributes = array();
        foreach ($data['attributes'] as $attr_name => $attr_values) {
            $attribute = new WC_Product_Attribute();
            $attribute->set_name($attr_name);
            $attribute->set_options($attr_values);
            $attribute->set_visible(true);
            $attribute->set_variation(false);
            $attributes[] = $attribute;
        }
        $product->set_attributes($attributes);
    }
    
    // Save product
    $product_id = $product->save();
    
    // Add custom meta
    if (isset($data['meta'])) {
        foreach ($data['meta'] as $key => $value) {
            update_post_meta($product_id, $key, $value);
        }
    }
    
    return $product_id;
}

// Products data
$products = array(
    array(
        'name' => 'Custom Coffee Bags - 250g',
        'sku' => 'ECO-COFFEE-250',
        'price' => '0.92',
        'short_description' => 'Premium coffee bags with one-way valve. Perfect for small batch roasters. Minimum 500 units.',
        'description' => '<h3>Premium Coffee Packaging</h3>
        <p>Our custom coffee bags are designed specifically for coffee roasters who demand quality and sustainability. Made from food-grade materials with optional one-way valves to keep your coffee fresh.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>Food-grade materials, FDA approved</li>
        <li>One-way valve options available</li>
        <li>Multiple barrier layers for freshness</li>
        <li>Custom printing with your branding</li>
        <li>Recyclable and compostable options</li>
        <li>Heat sealable</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Size: 250g (8oz) capacity</li>
        <li>Material: Kraft paper with barrier layer</li>
        <li>Minimum order: 500 units</li>
        <li>Delivery: 4-5 weeks</li>
        <li>Print colors: Up to 4 colors</li>
        </ul>',
        'categories' => array('Coffee Bags', 'Coffee & Café'),
        'attributes' => array(
            'Size' => array('250g', '500g', '1kg'),
            'Material' => array('Kraft Paper', 'Foil Lined'),
            'Valve' => array('With Valve', 'Without Valve'),
            'Minimum Order' => array('500 units')
        ),
        'meta' => array(
            '_min_quantity' => '500',
            '_delivery_time' => '4-5 weeks',
            '_price_from' => '$0.92',
            '_features' => 'Recyclable, Compostable, FDA Approved, One-way Valve',
            '_certifications' => 'FDA, FSC, BPI Compostable'
        )
    ),
    
    array(
        'name' => 'Custom Poly Mailers - 10x13"',
        'sku' => 'ECO-POLY-10X13',
        'price' => '0.56',
        'short_description' => 'Durable, weatherproof poly mailers. Made in USA with fast 1-week delivery. Minimum 100 units.',
        'description' => '<h3>Premium Poly Mailers</h3>
        <p>Our custom poly mailers are the perfect shipping solution for e-commerce businesses. Lightweight, waterproof, and fully customizable with your branding.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>100% waterproof and tear-resistant</li>
        <li>Self-sealing adhesive strip</li>
        <li>Lightweight to reduce shipping costs</li>
        <li>Full-color custom printing available</li>
        <li>Made in USA</li>
        <li>Recyclable through store drop-off programs</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Size: 10" x 13" (most popular)</li>
        <li>Material: 2.5 mil polyethylene</li>
        <li>Minimum order: 100 units</li>
        <li>Delivery: 1 week</li>
        <li>Colors: Full color printing available</li>
        </ul>',
        'categories' => array('Mailers', 'Shipping'),
        'attributes' => array(
            'Size' => array('6x9"', '10x13"', '14x17"', '19x24"'),
            'Thickness' => array('2.5 mil', '3.0 mil'),
            'Printing' => array('No Print', '1 Color', '2 Color', 'Full Color'),
            'Minimum Order' => array('100 units')
        ),
        'meta' => array(
            '_min_quantity' => '100',
            '_delivery_time' => '1 week',
            '_price_from' => '$0.56',
            '_features' => 'Made in USA, Waterproof, Recyclable, Fast Delivery',
            '_certifications' => 'Made in USA, Recyclable'
        )
    ),
    
    array(
        'name' => 'Custom Tissue Paper - Multi Color',
        'sku' => 'ECO-TISSUE-MULTI',
        'price' => '0.13',
        'short_description' => 'Premium tissue paper with full-color printing. Perfect for retail packaging. Minimum 500 units.',
        'description' => '<h3>Custom Tissue Paper</h3>
        <p>Add a premium touch to your packaging with our custom printed tissue paper. Perfect for retail, gifts, and e-commerce packaging.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>Full-color CMYK printing</li>
        <li>Acid-free paper</li>
        <li>Water-based inks</li>
        <li>FSC certified paper</li>
        <li>Recyclable and compostable</li>
        <li>Fade-resistant colors</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Size: 20" x 30" sheets</li>
        <li>Weight: 17gsm</li>
        <li>Minimum order: 500 sheets</li>
        <li>Delivery: 1 week</li>
        <li>Colors: Full CMYK printing</li>
        </ul>',
        'categories' => array('Tissue Paper', 'Retail'),
        'attributes' => array(
            'Size' => array('15x20"', '20x30"', '24x36"'),
            'Colors' => array('1 Color', '2 Colors', 'Full Color'),
            'Paper Weight' => array('17gsm', '20gsm'),
            'Minimum Order' => array('500 sheets')
        ),
        'meta' => array(
            '_min_quantity' => '500',
            '_delivery_time' => '1 week',
            '_price_from' => '$0.13',
            '_features' => 'Made in USA, Water-based Inks, FSC Certified, Recyclable',
            '_certifications' => 'FSC, Made in USA'
        )
    ),
    
    array(
        'name' => 'Custom Kraft Mailers',
        'sku' => 'ECO-KRAFT-MAILER',
        'price' => '0.59',
        'short_description' => 'Eco-friendly kraft mailers with custom printing. Compostable and reusable. Minimum 250 units.',
        'description' => '<h3>Sustainable Kraft Mailers</h3>
        <p>Our kraft mailers combine sustainability with functionality. Made from recycled kraft paper, these mailers are perfect for eco-conscious brands.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>Made from 100% recycled kraft paper</li>
        <li>Compostable and biodegradable</li>
        <li>Self-sealing adhesive strip</li>
        <li>Tear-resistant construction</li>
        <li>Custom printing with soy-based inks</li>
        <li>White ink printing available</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Sizes: Multiple sizes available</li>
        <li>Material: Recycled kraft paper</li>
        <li>Minimum order: 250 units</li>
        <li>Delivery: 3 weeks</li>
        <li>Printing: Soy-based inks</li>
        </ul>',
        'categories' => array('Mailers', 'Eco-Friendly'),
        'attributes' => array(
            'Size' => array('6x10"', '8.5x12"', '10x13"', '12x15.5"'),
            'Printing' => array('No Print', '1 Color', '2 Colors', 'White Ink'),
            'Material' => array('Natural Kraft', 'White Kraft'),
            'Minimum Order' => array('250 units')
        ),
        'meta' => array(
            '_min_quantity' => '250',
            '_delivery_time' => '3 weeks',
            '_price_from' => '$0.59',
            '_features' => 'Compostable, Recyclable, Reusable, FSC Certified, Soy-based Inks',
            '_certifications' => 'FSC, Compostable, Recyclable'
        )
    ),
    
    array(
        'name' => 'Custom Food Paper',
        'sku' => 'ECO-FOOD-PAPER',
        'price' => '0.02',
        'short_description' => 'Food-safe custom printed paper for restaurants and cafes. FDA approved. Minimum 250 units.',
        'description' => '<h3>Food-Safe Custom Paper</h3>
        <p>Perfect for restaurants, cafes, and food service businesses. Our food paper is FDA approved and safe for direct food contact.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>FDA approved for food contact</li>
        <li>Grease and moisture resistant</li>
        <li>Custom printing available</li>
        <li>Compostable material</li>
        <li>Water-based food-safe inks</li>
        <li>Various sizes available</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Sizes: 10x10" to 15x15"</li>
        <li>Material: Food-grade paper</li>
        <li>Minimum order: 250 sheets</li>
        <li>Delivery: 3 weeks</li>
        <li>Printing: Food-safe inks only</li>
        </ul>',
        'categories' => array('Food Paper', 'Restaurant'),
        'attributes' => array(
            'Size' => array('10x10"', '12x12"', '15x15"'),
            'Type' => array('Basket Liner', 'Deli Paper', 'Sandwich Wrap'),
            'Printing' => array('No Print', '1 Color', '2 Colors'),
            'Minimum Order' => array('250 sheets')
        ),
        'meta' => array(
            '_min_quantity' => '250',
            '_delivery_time' => '3 weeks',
            '_price_from' => '$0.02',
            '_features' => 'FDA Approved, Compostable, Food-safe, Water-based Inks',
            '_certifications' => 'FDA, Compostable, Food Safe'
        )
    ),
    
    array(
        'name' => 'Custom Sticker Rolls',
        'sku' => 'ECO-STICKER-ROLL',
        'price' => '0.01',
        'short_description' => 'Custom printed sticker rolls for branding and packaging. Various shapes and sizes. Minimum 1000 units.',
        'description' => '<h3>Custom Sticker Rolls</h3>
        <p>Perfect for branding, packaging seals, and promotional use. Our sticker rolls are made from sustainable materials with strong adhesive.</p>
        
        <h4>Features:</h4>
        <ul>
        <li>Strong permanent adhesive</li>
        <li>Weather-resistant materials</li>
        <li>Full-color printing available</li>
        <li>Various shapes and sizes</li>
        <li>Easy-peel backing</li>
        <li>Recyclable liner</li>
        </ul>
        
        <h4>Specifications:</h4>
        <ul>
        <li>Sizes: 1" to 4" diameter</li>
        <li>Material: Paper or vinyl</li>
        <li>Minimum order: 1000 stickers</li>
        <li>Delivery: 3 weeks</li>
        <li>Shapes: Round, square, custom die-cut</li>
        </ul>',
        'categories' => array('Stickers', 'Branding'),
        'attributes' => array(
            'Size' => array('1"', '2"', '3"', '4"'),
            'Shape' => array('Round', 'Square', 'Custom'),
            'Material' => array('Paper', 'Vinyl', 'Clear'),
            'Minimum Order' => array('1000 stickers')
        ),
        'meta' => array(
            '_min_quantity' => '1000',
            '_delivery_time' => '3 weeks',
            '_price_from' => '$0.01',
            '_features' => 'Weather-resistant, Strong Adhesive, Custom Shapes, Full Color',
            '_certifications' => 'Recyclable, FSC Paper'
        )
    )
);

// Create products
echo "<h1>Adding WooCommerce Products...</h1>";

foreach ($products as $product_data) {
    try {
        $product_id = create_packaging_product($product_data);
        echo "<p>✅ Created product: {$product_data['name']} (ID: {$product_id})</p>";
    } catch (Exception $e) {
        echo "<p>❌ Error creating {$product_data['name']}: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Products added successfully!</h2>";
echo "<p><a href='/shop'>View Shop</a> | <a href='/wp-admin/edit.php?post_type=product'>Manage Products</a></p>";
?>
