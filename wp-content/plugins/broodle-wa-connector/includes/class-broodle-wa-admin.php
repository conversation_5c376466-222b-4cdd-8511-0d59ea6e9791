<?php
/**
 * Admin interface class
 *
 * @package BroodleWAConnector
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Admin interface class
 */
class Broodle_WA_Admin {

    /**
     * Check if current user has permission to access plugin
     */
    private function user_can_access() {
        return current_user_can( 'manage_woocommerce' ) ||
               current_user_can( 'manage_options' ) ||
               current_user_can( 'edit_others_posts' ); // Editor capability
    }

    /**
     * Get WhatsApp icon for menu
     */
    private function get_whatsapp_icon() {
        return 'data:image/svg+xml;base64,' . base64_encode( '
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#25d366">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
            </svg>
        ' );
    }

    /**
     * Constructor
     */
    public function __construct() {
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        add_action( 'admin_init', array( $this, 'register_settings' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );
        add_action( 'wp_ajax_broodle_wa_test_api', array( $this, 'ajax_test_api' ) );
        add_action( 'wp_ajax_broodle_wa_send_test_message', array( $this, 'ajax_send_test_message' ) );
        add_action( 'wp_ajax_broodle_wa_quick_test', array( $this, 'ajax_quick_test' ) );
        add_action( 'wp_ajax_broodle_wa_test_failed_notification', array( $this, 'ajax_test_failed_notification' ) );





        // Add diagnostic notice for order statuses (only on plugin pages)
        add_action( 'admin_notices', array( $this, 'show_status_diagnostic' ) );

        // Add dashboard widget
        add_action( 'wp_dashboard_setup', array( $this, 'add_dashboard_widget' ) );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __( 'Broodle WA', 'broodle-wa-connector' ),
            __( 'Broodle WA', 'broodle-wa-connector' ),
            'manage_woocommerce',
            'broodle-wa-connector',
            array( $this, 'admin_page' ),
            $this->get_whatsapp_icon(),
            56 // Position after WooCommerce (which is at 55.5)
        );

        // Add submenu pages with proper callback handling
        add_submenu_page(
            'broodle-wa-connector',
            __( 'Settings', 'broodle-wa-connector' ),
            __( 'Settings', 'broodle-wa-connector' ),
            'manage_woocommerce',
            'broodle-wa-connector',
            array( $this, 'admin_page' )
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting(
            'broodle_wa_settings_group',
            'broodle_wa_settings',
            array( $this, 'sanitize_settings' )
        );
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook_suffix Current admin page hook suffix.
     */
    public function enqueue_admin_scripts( $hook_suffix ) {
        if ( 'toplevel_page_broodle-wa-connector' !== $hook_suffix ) {
            return;
        }

        wp_enqueue_script(
            'broodle-wa-admin',
            BROODLE_WA_PLUGIN_URL . 'assets/js/admin.js',
            array( 'jquery' ),
            BROODLE_WA_VERSION,
            true
        );

        wp_enqueue_style(
            'broodle-wa-admin',
            BROODLE_WA_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            BROODLE_WA_VERSION
        );

        // Add custom menu styling and WhatsApp icon
        wp_add_inline_style( 'broodle-wa-admin', '
            #adminmenu .menu-icon-broodle-wa-connector div.wp-menu-image img {
                width: 20px;
                height: 20px;
                opacity: 0.6;
            }
            #adminmenu .menu-icon-broodle-wa-connector:hover div.wp-menu-image img,
            #adminmenu .menu-icon-broodle-wa-connector.wp-has-current-submenu div.wp-menu-image img {
                opacity: 1;
            }
            .dashicons-whatsapp:before {
                content: "💬";
                font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
                font-size: 18px;
                line-height: 1;
            }



            /* Fix main menu layout issues */
            .broodle-wa-admin .nav-tab-wrapper {
                border-bottom: 1px solid #ccd0d4;
                margin: 0;
                padding-top: 9px;
                padding-bottom: 0;
                line-height: inherit;
            }

            .broodle-wa-admin .tab-content {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-top: none;
                padding: 20px;
                margin: 0;
            }

            .broodle-wa-admin .nav-tab {
                border: 1px solid #ccd0d4;
                border-bottom: none;
                background: #f1f1f1;
                color: #666;
                margin-left: 0.25em;
                padding: 10px 15px;
                text-decoration: none;
                font-weight: 600;
            }

            .broodle-wa-admin .nav-tab:hover {
                background-color: #fff;
                color: #464646;
            }

            .broodle-wa-admin .nav-tab-active {
                background: #fff;
                border-bottom: 1px solid #fff;
                color: #000;
                margin-bottom: -1px;
                padding-bottom: 11px;
            }

            .broodle-wa-admin .wrap {
                margin: 10px 20px 0 2px;
            }

            /* Responsive design for mobile */
            @media (max-width: 782px) {
                .broodle-wa-admin .stats-grid {
                    grid-template-columns: 1fr !important;
                    gap: 15px !important;
                }

                .broodle-wa-admin .nav-tab {
                    margin-bottom: 5px;
                    display: block;
                    text-align: center;
                }

                .broodle-wa-admin .wrap {
                    margin: 10px 10px 0 10px;
                }
            }

            @media (max-width: 600px) {
                .broodle-wa-admin .stats-grid {
                    grid-template-columns: 1fr !important;
                }
            }

            /* Settings page specific styling */
            .broodle-wa-admin .form-table {
                background: #fff;
                border: 1px solid #ccd0d4;
                border-radius: 4px;
                padding: 30px;
                margin-top: 20px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            }

            .broodle-wa-admin .form-table th {
                width: 220px;
                padding: 20px 20px 20px 20px !important;
                vertical-align: top;
                font-weight: 600;
                color: #23282d;
                text-align: left;
            }

            .broodle-wa-admin .form-table td {
                padding: 20px 20px 20px 10px !important;
            }

            .broodle-wa-admin .form-table tr {
                border-bottom: 1px solid #f0f0f1;
            }

            .broodle-wa-admin .form-table tr:last-child {
                border-bottom: none;
            }

            /* Override WordPress default form-table styles */
            .broodle-wa-admin .form-table th,
            .broodle-wa-admin .form-table td {
                border: none !important;
            }

            .broodle-wa-admin .form-table th {
                padding-left: 20px !important;
                box-sizing: border-box;
            }

            .broodle-wa-admin .form-table td {
                padding-right: 20px !important;
                box-sizing: border-box;
            }

            /* Ensure proper spacing before submit button */
            .broodle-wa-admin form .submit {
                margin-top: 0 !important;
            }

            .broodle-wa-admin .form-table {
                margin-bottom: 0 !important;
            }

            /* API Key Section Styling */
            .broodle-api-key-section {
                max-width: 700px;
                background: #fff;
                padding: 20px;
                border: 1px solid #e5e5e5;
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            }

            .api-key-row {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 30px;
                padding-bottom: 25px;
                border-bottom: 2px solid #e5e5e5;
            }

            .broodle-api-input {
                width: 450px !important;
                font-family: Consolas, Monaco, monospace !important;
                font-size: 13px !important;
                padding: 10px 14px !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                background-color: #fafafa !important;
            }

            .broodle-toggle-btn {
                min-width: 70px !important;
                padding: 10px 14px !important;
                font-size: 13px !important;
                height: auto !important;
                line-height: 1.2 !important;
                border-radius: 4px !important;
            }

            .api-test-section {
                margin-top: 20px;
                padding-top: 20px;
                background: #f9f9f9;
                padding: 20px;
                border-radius: 5px;
                border: 1px solid #e0e0e0;
            }

            .api-test-row {
                display: flex;
                align-items: center;
                gap: 15px;
                flex-wrap: wrap;
            }

            .api-test-row .button {
                margin: 0 !important;
                vertical-align: middle;
                padding: 10px 18px !important;
                font-weight: 500 !important;
            }

            /* API test result */
            #api-test-result {
                padding: 10px 15px;
                border-radius: 5px;
                font-size: 13px;
                font-weight: 500;
                min-width: 200px;
                display: inline-block;
                vertical-align: middle;
                margin-left: 15px;
                transition: all 0.3s ease;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            #api-test-result.success {
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                color: #155724;
            }

            #api-test-result.error {
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                color: #721c24;
            }

            #api-test-result.loading {
                background-color: #d1ecf1;
                border: 1px solid #bee5eb;
                color: #0c5460;
            }

            #api-test-result:empty {
                display: none !important;
            }

            #api-test-result.success:before {
                content: "✓ ";
                font-weight: bold;
                margin-right: 5px;
            }

            #api-test-result.error:before {
                content: "✗ ";
                font-weight: bold;
                margin-right: 5px;
            }

            #api-test-result.loading:before {
                content: "⟳ ";
                font-weight: bold;
                margin-right: 5px;
                animation: broodle-spin 1s linear infinite;
            }

            @keyframes broodle-spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* Submit button styling */
            .broodle-wa-admin .submit {
                padding: 25px 30px 20px 30px !important;
                margin: 0 !important;
                background: #f9f9f9 !important;
                border-top: 1px solid #e0e0e0 !important;
                border-radius: 0 0 4px 4px !important;
                margin-top: 0 !important;
                text-align: left !important;
            }

            /* Override WordPress default submit button container */
            .broodle-wa-admin p.submit {
                padding: 25px 30px 20px 30px !important;
                margin: 0 !important;
                background: #f9f9f9 !important;
                border-top: 1px solid #e0e0e0 !important;
                border-radius: 0 0 4px 4px !important;
                text-align: left !important;
            }

            .broodle-wa-admin .submit .button-primary,
            .broodle-wa-admin p.submit .button-primary {
                padding: 12px 24px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                border-radius: 4px !important;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
                margin: 0 !important;
            }

            /* Ensure no margin on submit button container */
            .broodle-wa-admin form > p.submit:last-child {
                margin-bottom: 0 !important;
                margin-top: 0 !important;
            }

            /* Form field descriptions */
            .broodle-wa-admin .form-table .description {
                margin-top: 8px;
                color: #646970;
                font-size: 13px;
                line-height: 1.4;
            }

            /* Select and input field styling */
            .broodle-wa-admin .form-table select,
            .broodle-wa-admin .form-table input[type="text"],
            .broodle-wa-admin .form-table input[type="number"] {
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                min-width: 250px;
            }

            .broodle-wa-admin .form-table select:focus,
            .broodle-wa-admin .form-table input[type="text"]:focus,
            .broodle-wa-admin .form-table input[type="number"]:focus {
                border-color: #0073aa;
                box-shadow: 0 0 0 1px #0073aa;
                outline: none;
            }

            /* Mobile responsive for settings */
            @media (max-width: 768px) {
                .broodle-wa-admin .form-table {
                    padding: 20px;
                }

                .broodle-wa-admin .form-table th {
                    width: auto;
                    display: block;
                    padding: 15px 20px 5px 20px !important;
                }

                .broodle-wa-admin .form-table td {
                    display: block;
                    padding: 5px 20px 15px 20px !important;
                }

                .broodle-api-key-section {
                    padding: 15px;
                }

                .api-key-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 12px;
                    margin-bottom: 30px;
                }

                .broodle-api-input {
                    width: 100% !important;
                    max-width: 350px !important;
                }

                .api-test-section {
                    margin-top: 25px;
                    padding: 15px;
                }

                .api-test-row {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 12px;
                }

                #api-test-result {
                    margin-left: 0;
                    margin-top: 8px;
                    width: 100%;
                    max-width: 350px;
                }

                .broodle-wa-admin .form-table select,
                .broodle-wa-admin .form-table input[type="text"],
                .broodle-wa-admin .form-table input[type="number"] {
                    min-width: 100%;
                    max-width: 350px;
                }
            }
        ' );

        // Enqueue media scripts for image selection
        wp_enqueue_media();
        wp_enqueue_script( 'wp-media' );

        wp_localize_script(
            'broodle-wa-admin',
            'broodle_wa_admin',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'broodle_wa_admin_nonce' ),
                'strings' => array(
                    'testing_api' => __( 'Testing API connection...', 'broodle-wa-connector' ),
                    'api_test_success' => __( 'API connection successful!', 'broodle-wa-connector' ),
                    'api_test_failed' => __( 'API connection failed. Please check your credentials.', 'broodle-wa-connector' ),
                    'sending_test' => __( 'Sending test message...', 'broodle-wa-connector' ),
                    'test_sent' => __( 'Test message sent successfully!', 'broodle-wa-connector' ),
                    'test_failed' => __( 'Failed to send test message.', 'broodle-wa-connector' ),
                ),
            )
        );
    }

    /**
     * Admin page callback
     */
    public function admin_page() {
        // Check user permissions
        if ( ! $this->user_can_access() ) {
            wp_die( __( 'You do not have sufficient permissions to access this page.', 'broodle-wa-connector' ) );
        }

        $active_tab = isset( $_GET['tab'] ) ? sanitize_text_field( $_GET['tab'] ) : 'settings';
        ?>
        <div class="wrap broodle-wa-admin">
            <h1 style="display: flex; align-items: center;">
                <img src="<?php echo esc_url( BROODLE_WA_PLUGIN_URL . 'assets/images/broodle-logo.png' ); ?>"
                     alt="Broodle Logo"
                     style="max-width: 80px; height: auto; margin-right: 12px; vertical-align: middle;" />
                <?php esc_html_e( 'Broodle WA Connector', 'broodle-wa-connector' ); ?>
            </h1>

            <h2 class="nav-tab-wrapper">
                <a href="?page=broodle-wa-connector&tab=settings" class="nav-tab <?php echo 'settings' === $active_tab ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e( 'Settings', 'broodle-wa-connector' ); ?>
                </a>
                <a href="?page=broodle-wa-connector&tab=templates" class="nav-tab <?php echo 'templates' === $active_tab ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e( 'Templates', 'broodle-wa-connector' ); ?>
                </a>
                <a href="?page=broodle-wa-connector&tab=logs" class="nav-tab <?php echo 'logs' === $active_tab ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e( 'Logs', 'broodle-wa-connector' ); ?>
                </a>
                <a href="?page=broodle-wa-connector&tab=help" class="nav-tab <?php echo 'help' === $active_tab ? 'nav-tab-active' : ''; ?>">
                    <?php esc_html_e( 'Help', 'broodle-wa-connector' ); ?>
                </a>
            </h2>

            <div class="tab-content">
                <?php
                switch ( $active_tab ) {
                    case 'templates':
                        $this->render_templates_tab();
                        break;
                    case 'logs':
                        $this->render_logs_tab();
                        break;
                    case 'help':
                        $this->render_help_tab();
                        break;
                    default:
                        $this->render_settings_tab();
                        break;
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render settings tab
     */
    private function render_settings_tab() {
        $settings = Broodle_WA_Settings::get_settings();
        ?>
        <style>
        /* Settings Page Layout Optimization */
        .wrap { max-width: none !important; margin-right: 20px !important; }
        .tab-content { max-width: none !important; }

        /* Settings Page Header */
        .settings-page-header {
            margin-bottom: 30px !important;
            padding: 20px 0 !important;
            border-bottom: 1px solid #e1e5e9 !important;
        }
        .settings-page-title {
            margin: 0 0 12px 0 !important;
            font-size: 24px !important;
            font-weight: 600 !important;
            color: #1d2327 !important;
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
        }
        .settings-page-title .dashicons {
            font-size: 28px !important;
            width: 28px !important;
            height: 28px !important;
            color: #2271b1 !important;
        }
        .settings-page-description {
            margin: 0 !important;
            color: #646970 !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            max-width: 800px !important;
        }

        /* Enhanced Form Table */
        .settings-form-table {
            background: #fff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            padding: 0 !important;
            margin: 20px 0 !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            overflow: hidden !important;
        }
        .settings-form-table th {
            background: #f8f9fa !important;
            border-bottom: 1px solid #e1e5e9 !important;
            padding: 20px 24px !important;
            font-weight: 600 !important;
            color: #1d2327 !important;
            font-size: 14px !important;
            width: 25% !important;
            vertical-align: top !important;
        }
        .settings-form-table td {
            padding: 20px 24px !important;
            border-bottom: 1px solid #f0f0f1 !important;
            vertical-align: top !important;
        }
        .settings-form-table tr:last-child th,
        .settings-form-table tr:last-child td {
            border-bottom: none !important;
        }
        .settings-form-table tr:hover {
            background: #f8f9fa !important;
        }

        /* API Key Section Enhancement */
        .broodle-api-key-section {
            display: flex !important;
            flex-direction: column !important;
            gap: 16px !important;
        }
        .api-key-row {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            flex-wrap: wrap !important;
        }
        .broodle-api-input {
            flex: 1 !important;
            min-width: 300px !important;
            max-width: 500px !important;
            padding: 10px 12px !important;
            border: 1px solid #8c8f94 !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }
        .broodle-api-input:focus {
            border-color: #2271b1 !important;
            box-shadow: 0 0 0 1px #2271b1 !important;
            outline: none !important;
        }
        .broodle-toggle-btn {
            padding: 8px 16px !important;
            font-size: 13px !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
        }
        .broodle-toggle-btn:hover {
            background: #f0f6fc !important;
            border-color: #2271b1 !important;
            color: #2271b1 !important;
        }

        /* API Test Section */
        .api-test-section {
            background: #f6f7f7 !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 6px !important;
            padding: 16px !important;
        }
        .api-test-row {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            flex-wrap: wrap !important;
        }
        .api-test-section .button {
            padding: 8px 16px !important;
            font-size: 13px !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
        }
        .api-test-section .button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        }
        #api-test-result {
            margin-left: 12px !important;
            font-weight: 500 !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
        }
        #api-test-result.success {
            background: #d1e7dd !important;
            color: #0f5132 !important;
            border: 1px solid #badbcc !important;
        }
        #api-test-result.error {
            background: #f8d7da !important;
            color: #721c24 !important;
            border: 1px solid #f5c2c7 !important;
        }

        /* Form Input Enhancements */
        .settings-form-table input[type="text"],
        .settings-form-table input[type="number"],
        .settings-form-table input[type="password"],
        .settings-form-table select {
            padding: 8px 12px !important;
            border: 1px solid #8c8f94 !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }
        .settings-form-table input[type="text"]:focus,
        .settings-form-table input[type="number"]:focus,
        .settings-form-table input[type="password"]:focus,
        .settings-form-table select:focus {
            border-color: #2271b1 !important;
            box-shadow: 0 0 0 1px #2271b1 !important;
            outline: none !important;
        }
        .settings-form-table .small-text {
            width: 120px !important;
        }

        /* Description Text Enhancement */
        .settings-form-table .description {
            margin-top: 8px !important;
            color: #646970 !important;
            font-size: 13px !important;
            line-height: 1.4 !important;
        }
        .settings-form-table .description a {
            color: #2271b1 !important;
            text-decoration: none !important;
        }
        .settings-form-table .description a:hover {
            text-decoration: underline !important;
        }

        /* Submit Button Enhancement */
        .settings-submit-section {
            background: #fff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            padding: 20px 24px !important;
            margin: 20px 0 !important;
            text-align: right !important;
        }
        .settings-submit-section .button-primary {
            padding: 10px 24px !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
        }
        .settings-submit-section .button-primary:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 4px rgba(34, 113, 177, 0.3) !important;
        }

        /* Responsive Design for Settings Page */
        @media (max-width: 1200px) {
            .settings-form-table th {
                width: 30% !important;
            }
            .broodle-api-input {
                min-width: 250px !important;
                max-width: 400px !important;
            }
        }

        @media (max-width: 782px) {
            .wrap { margin-right: 10px !important; }
            .settings-page-header { padding: 16px 0 !important; margin-bottom: 20px !important; }
            .settings-page-title {
                font-size: 20px !important;
                flex-direction: column !important;
                text-align: center !important;
                gap: 8px !important;
            }
            .settings-page-title .dashicons { font-size: 24px !important; }
            .settings-page-description {
                text-align: center !important;
                font-size: 13px !important;
            }

            /* Mobile Table Layout */
            .settings-form-table,
            .settings-form-table tbody,
            .settings-form-table tr,
            .settings-form-table td,
            .settings-form-table th {
                display: block !important;
                width: 100% !important;
            }
            .settings-form-table th {
                background: #f8f9fa !important;
                border-bottom: 1px solid #e1e5e9 !important;
                padding: 16px 20px 8px 20px !important;
                font-size: 15px !important;
                text-align: left !important;
            }
            .settings-form-table td {
                padding: 8px 20px 20px 20px !important;
                border-bottom: 2px solid #f0f0f1 !important;
            }

            .api-key-row {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 12px !important;
            }
            .broodle-api-input {
                min-width: auto !important;
                max-width: none !important;
                width: 100% !important;
            }
            .api-test-row {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 12px !important;
            }
            .api-test-section .button {
                width: 100% !important;
                text-align: center !important;
            }
            #api-test-result {
                margin-left: 0 !important;
                margin-top: 8px !important;
                text-align: center !important;
            }

            .settings-form-table .small-text {
                width: 100% !important;
                max-width: 200px !important;
            }
            .settings-submit-section {
                text-align: center !important;
                padding: 16px 20px !important;
            }
        }

        @media (max-width: 600px) {
            .settings-form-table th,
            .settings-form-table td {
                padding: 12px 16px !important;
            }
            .api-test-section {
                padding: 12px !important;
            }
            .settings-page-title {
                font-size: 18px !important;
            }
        }

        @media (max-width: 480px) {
            .wrap { margin-right: 5px !important; }
            .settings-page-header { padding: 12px 0 !important; }
            .settings-form-table th,
            .settings-form-table td {
                padding: 10px 12px !important;
            }
            .api-test-section {
                padding: 10px !important;
            }
            .settings-submit-section {
                padding: 12px 16px !important;
            }
        }
        </style>

        <div class="settings-page-header">
            <h2 class="settings-page-title">
                <span class="dashicons dashicons-admin-settings"></span>
                <?php esc_html_e( 'WhatsApp Connector Settings', 'broodle-wa-connector' ); ?>
            </h2>
            <p class="settings-page-description">
                <?php esc_html_e( 'Configure your WhatsApp API connection and notification preferences. Make sure to test your connection after entering your API key.', 'broodle-wa-connector' ); ?>
            </p>
        </div>

        <form method="post" action="options.php">
            <?php
            settings_fields( 'broodle_wa_settings_group' );
            wp_nonce_field( 'broodle_wa_settings_nonce', 'broodle_wa_settings_nonce' );
            ?>

            <table class="form-table settings-form-table">
                <tr>
                    <th scope="row">
                        <label for="api_key"><?php esc_html_e( 'API Key', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <div class="broodle-api-key-section">
                            <div class="api-key-row">
                                <input type="password" id="api_key" name="broodle_wa_settings[api_key]" value="<?php echo esc_attr( $settings['api_key'] ); ?>" class="broodle-api-input" />
                                <button type="button" id="toggle-api-key" class="button button-secondary broodle-toggle-btn" onclick="broodleToggleApiKey()">
                                    <?php esc_html_e( 'Show', 'broodle-wa-connector' ); ?>
                                </button>
                            </div>
                            <div class="api-test-section">
                                <div class="api-test-row">
                                    <button type="button" id="test-api-connection" class="button button-secondary">
                                        <?php esc_html_e( 'Test Connection', 'broodle-wa-connector' ); ?>
                                    </button>
                                    <button type="button" id="quick-test-message" class="button button-secondary">
                                        <?php esc_html_e( 'Quick Test Message', 'broodle-wa-connector' ); ?>
                                    </button>
                                    <span id="api-test-result"></span>
                                </div>
                            </div>
                        </div>

                        <script type="text/javascript">
                        function broodleToggleApiKey() {
                            var input = document.getElementById('api_key');
                            var button = document.getElementById('toggle-api-key');

                            if (input.type === 'password') {
                                input.type = 'text';
                                button.textContent = 'Hide';
                            } else {
                                input.type = 'password';
                                button.textContent = 'Show';
                            }
                        }
                        </script>
                        <p class="description">
                            <?php
                            printf(
                                /* translators: %s: Registration URL */
                                esc_html__( 'Get your API key from %s', 'broodle-wa-connector' ),
                                '<a href="https://wa.broodle.one" target="_blank">https://wa.broodle.one</a>'
                            );
                            ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="phone_field"><?php esc_html_e( 'Phone Number Field', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <select id="phone_field" name="broodle_wa_settings[phone_field]">
                            <?php foreach ( Broodle_WA_Settings::get_phone_field_options() as $value => $label ) : ?>
                                <option value="<?php echo esc_attr( $value ); ?>" <?php selected( $settings['phone_field'], $value ); ?>>
                                    <?php echo esc_html( $label ); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php esc_html_e( 'Choose which phone number field to use for WhatsApp notifications.', 'broodle-wa-connector' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="country_code"><?php esc_html_e( 'Default Country Code', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <input type="text" id="country_code" name="broodle_wa_settings[country_code]" value="<?php echo esc_attr( $settings['country_code'] ); ?>" class="small-text" placeholder="+1" />
                        <p class="description"><?php esc_html_e( 'Default country code to use if not specified in phone number.', 'broodle-wa-connector' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="retry_attempts"><?php esc_html_e( 'Retry Attempts', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <input type="number" id="retry_attempts" name="broodle_wa_settings[retry_attempts]" value="<?php echo esc_attr( $settings['retry_attempts'] ); ?>" min="0" max="10" class="small-text" />
                        <p class="description"><?php esc_html_e( 'Number of times to retry failed notifications.', 'broodle-wa-connector' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="retry_delay"><?php esc_html_e( 'Retry Delay (seconds)', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <input type="number" id="retry_delay" name="broodle_wa_settings[retry_delay]" value="<?php echo esc_attr( $settings['retry_delay'] ); ?>" min="60" max="3600" class="small-text" />
                        <p class="description"><?php esc_html_e( 'Delay between retry attempts in seconds.', 'broodle-wa-connector' ); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="log_retention_days"><?php esc_html_e( 'Log Retention (days)', 'broodle-wa-connector' ); ?></label>
                    </th>
                    <td>
                        <input type="number" id="log_retention_days" name="broodle_wa_settings[log_retention_days]" value="<?php echo esc_attr( $settings['log_retention_days'] ); ?>" min="1" max="365" class="small-text" />
                        <p class="description"><?php esc_html_e( 'Number of days to keep notification logs.', 'broodle-wa-connector' ); ?></p>
                    </td>
                </tr>
            </table>

            <div class="settings-submit-section">
                <?php submit_button( __( 'Save Settings', 'broodle-wa-connector' ), 'primary', 'submit', false ); ?>
            </div>
        </form>
        <?php
    }

    /**
     * Render templates tab
     */
    private function render_templates_tab() {
        $settings = Broodle_WA_Settings::get_settings();
        ?>
        <style>
        /* Page Header Styling */
        .broodle-page-header { margin-bottom: 30px !important; padding: 20px 0 !important; border-bottom: 1px solid #e1e5e9 !important; }
        .broodle-page-title { margin: 0 0 12px 0 !important; font-size: 24px !important; font-weight: 600 !important; color: #1d2327 !important; display: flex !important; align-items: center !important; gap: 12px !important; }
        .broodle-page-title .dashicons { font-size: 28px !important; width: 28px !important; height: 28px !important; color: #25d366 !important; }
        .broodle-page-description { margin: 0 !important; font-size: 14px !important; color: #646970 !important; line-height: 1.5 !important; max-width: 800px !important; }

        /* Main Container Improvements */
        .wrap { max-width: none !important; margin-right: 20px !important; }
        .form-table { max-width: none !important; }

        /* Template Table Styling */
        .template-config-table { background: #fff !important; border: 1px solid #c3c4c7 !important; border-radius: 8px !important; margin-top: 20px !important; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; }
        .template-row-wrapper { border-bottom: 1px solid #f0f0f1 !important; }
        .template-header-cell { background: #f8f9fa !important; padding: 24px !important; width: 300px !important; vertical-align: top !important; border-right: 1px solid #f0f0f1 !important; }
        .template-title { margin: 0 !important; font-size: 16px !important; font-weight: 600 !important; color: #1d2327 !important; }
        .template-toggle-wrapper { display: flex !important; align-items: center !important; gap: 8px !important; cursor: pointer !important; margin-top: 12px !important; }
        .template-checkbox { display: none !important; }
        .template-toggle-slider { position: relative !important; width: 44px !important; height: 24px !important; background: #8c8f94 !important; border-radius: 24px !important; transition: background 0.3s ease !important; cursor: pointer !important; }
        .template-toggle-slider:before { content: '' !important; position: absolute !important; top: 3px !important; left: 3px !important; width: 18px !important; height: 18px !important; background: white !important; border-radius: 50% !important; transition: transform 0.3s ease !important; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important; }
        .template-checkbox:checked + .template-toggle-slider { background: #2271b1 !important; }
        .template-checkbox:checked + .template-toggle-slider:before { transform: translateX(20px) !important; }
        .toggle-label { font-size: 14px !important; font-weight: 500 !important; color: #1d2327 !important; }
        .template-content-cell { padding: 24px !important; vertical-align: top !important; }
        .template-name-input-group { display: flex !important; gap: 12px !important; align-items: center !important; margin-top: 8px !important; flex-wrap: wrap !important; }
        .template-name-input { flex: 1 !important; min-width: 300px !important; max-width: 500px !important; }
        /* Variables Accordion Styling */
        .variables-accordion-toggle { width: 100% !important; background: none !important; border: none !important; padding: 14px 0 !important; display: flex !important; justify-content: space-between !important; align-items: center !important; cursor: pointer !important; font-size: 14px !important; font-weight: 600 !important; color: #2271b1 !important; text-align: left !important; border-top: 1px solid #f0f0f1 !important; margin-top: 20px !important; outline: none !important; }
        .variables-accordion-toggle:focus { outline: none !important; box-shadow: none !important; }
        .variables-accordion-toggle:active { background: #f0f0f1 !important; }
        .variables-accordion-toggle:hover { color: #135e96 !important; }
        .variables-accordion-content { padding-top: 20px !important; border-top: 1px solid #f0f0f1 !important; margin-top: 12px !important; animation: none !important; }
        .variables-accordion-content.hidden { display: none !important; }
        .variables-accordion-content.visible { display: block !important; }

        /* Improved Variables Grid Layout */
        .variables-selection-grid {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
            gap: 20px !important;
            margin-bottom: 16px !important;
        }
        .variable-select-group {
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;
        }
        .variable-select-label {
            font-weight: 500 !important;
            color: #1d2327 !important;
            font-size: 13px !important;
            margin-bottom: 4px !important;
        }
        .variable-select {
            padding: 8px 12px !important;
            border: 1px solid #8c8f94 !important;
            border-radius: 4px !important;
            background: #fff !important;
            font-size: 13px !important;
            min-height: 36px !important;
            transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
        }
        .variable-select:focus {
            border-color: #2271b1 !important;
            box-shadow: 0 0 0 1px #2271b1 !important;
            outline: none !important;
        }
        .variable-select:hover {
            border-color: #646970 !important;
        }
        .variables-description { margin: 0 0 16px 0 !important; color: #646970 !important; font-size: 13px !important; background: #f6f7f7 !important; padding: 12px 16px !important; border-radius: 4px !important; border-left: 4px solid #72aee6 !important; }
        .accordion-arrow { transition: transform 0.3s ease !important; font-size: 12px !important; display: inline-block !important; }
        .variables-accordion-toggle.active .accordion-arrow { transform: rotate(180deg) !important; }

        /* Status Mapping Styling */
        .template-status-mapping { margin-top: 16px !important; padding-top: 16px !important; border-top: 1px solid #f0f0f1 !important; }
        .status-mapping-select { margin-top: 8px !important; width: 100% !important; max-width: 300px !important; }
        .template-status-mapping .description { margin-top: 8px !important; font-size: 13px !important; color: #646970 !important; font-style: italic !important; }

        /* Image Selection Styling */
        .template-image-selection { margin-top: 16px !important; padding-top: 16px !important; border-top: 1px solid #f0f0f1 !important; }
        .template-image-input-group { display: flex !important; align-items: flex-start !important; gap: 15px !important; margin-top: 8px !important; }
        .template-image-preview { flex-shrink: 0 !important; width: 120px !important; height: 120px !important; border: 2px dashed #ddd !important; border-radius: 8px !important; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; background: #fafafa !important; position: relative !important; }
        .template-image-preview img { max-width: 100px !important; max-height: 100px !important; border-radius: 4px !important; object-fit: cover !important; }
        .template-image-preview .image-filename { margin: 5px 0 0 0 !important; font-size: 11px !important; color: #666 !important; text-align: center !important; word-break: break-all !important; }
        .no-image-placeholder { text-align: center !important; color: #999 !important; }
        .no-image-placeholder .dashicons { font-size: 32px !important; width: 32px !important; height: 32px !important; margin-bottom: 8px !important; }
        .no-image-placeholder p { margin: 0 !important; font-size: 12px !important; }
        .template-image-buttons { display: flex !important; flex-direction: column !important; gap: 8px !important; }
        .template-image-buttons .button { min-width: 100px !important; justify-content: center !important; }
        .template-image-selection .description { margin-top: 8px !important; font-size: 13px !important; color: #646970 !important; font-style: italic !important; }

        /* Coupon Code Styling */
        .template-coupon-code { margin-top: 16px !important; padding-top: 16px !important; border-top: 1px solid #f0f0f1 !important; }
        .template-coupon-input-group { display: flex !important; gap: 10px !important; margin-top: 8px !important; align-items: flex-start !important; }
        .template-coupon-input { flex: 1 !important; max-width: 200px !important; }
        .template-coupon-position-select { min-width: 140px !important; }
        .template-coupon-code .description { margin-top: 8px !important; font-size: 13px !important; color: #646970 !important; font-style: italic !important; }

        /* Delay Styling */
        .template-delay { margin-top: 16px !important; padding-top: 16px !important; border-top: 1px solid #f0f0f1 !important; }
        .template-delay-select { margin-top: 8px !important; min-width: 150px !important; }
        .template-delay .description { margin-top: 8px !important; font-size: 13px !important; color: #646970 !important; font-style: italic !important; }

        /* Status Badge Styling */
        .status-badge { padding: 4px 8px !important; border-radius: 3px !important; font-size: 12px !important; font-weight: 600 !important; text-transform: uppercase !important; }
        .status-success { background-color: #d4edda !important; color: #155724 !important; border: 1px solid #c3e6cb !important; }
        .status-error { background-color: #f8d7da !important; color: #721c24 !important; border: 1px solid #f5c6cb !important; }
        .status-pending { background-color: #fff3cd !important; color: #856404 !important; border: 1px solid #ffeaa7 !important; }
        .status-retry { background-color: #e2e3e5 !important; color: #383d41 !important; border: 1px solid #d6d8db !important; }
        .status-scheduled { background-color: #cce5ff !important; color: #004085 !important; border: 1px solid #99d6ff !important; }

        /* Scheduled Notifications Styling */
        .scheduled-notifications-section { margin-bottom: 30px !important; }
        .overdue-notification { background-color: #fff2f2 !important; }
        .count { font-size: 14px !important; font-weight: normal !important; color: #646970 !important; }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .variables-selection-grid {
                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)) !important;
                gap: 16px !important;
            }
        }

        @media (max-width: 782px) {
            .wrap { margin-right: 10px !important; }
            .broodle-page-header { padding: 16px 0 !important; margin-bottom: 20px !important; }
            .broodle-page-title { font-size: 20px !important; flex-direction: column !important; text-align: center !important; gap: 8px !important; }
            .broodle-page-title .dashicons { font-size: 24px !important; width: 24px !important; height: 24px !important; }
            .broodle-page-description { text-align: center !important; font-size: 13px !important; }

            .template-config-table,
            .template-config-table tbody,
            .template-config-table tr,
            .template-config-table td,
            .template-config-table th {
                display: block !important;
                width: 100% !important;
            }

            .template-row-wrapper {
                margin-bottom: 20px !important;
                border: 1px solid #e1e5e9 !important;
                border-radius: 8px !important;
                overflow: hidden !important;
            }

            .template-header-cell {
                border-right: none !important;
                border-bottom: 1px solid #f0f0f1 !important;
                width: 100% !important;
                padding: 18px !important;
            }

            .template-content-cell {
                padding: 18px !important;
            }

            .template-name-input-group {
                flex-direction: column !important;
                align-items: stretch !important;
                gap: 12px !important;
            }

            .template-name-input {
                max-width: none !important;
                min-width: auto !important;
            }

            .variables-selection-grid {
                grid-template-columns: 1fr !important;
                gap: 16px !important;
            }

            .variables-accordion-toggle {
                padding: 16px 0 !important;
                font-size: 16px !important;
            }

            .variables-accordion-content {
                padding-top: 20px !important;
            }

            .template-image-input-group {
                flex-direction: column !important;
                align-items: center !important;
                gap: 12px !important;
            }

            .template-image-preview {
                width: 100px !important;
                height: 100px !important;
            }

            .template-coupon-input-group {
                flex-direction: column !important;
                gap: 8px !important;
            }

            .template-coupon-input {
                max-width: none !important;
            }

            .template-coupon-position-select {
                min-width: auto !important;
            }
        }

        @media (max-width: 600px) {
            .variables-selection-grid {
                grid-template-columns: 1fr !important;
                gap: 14px !important;
            }
            .variable-select {
                font-size: 14px !important;
                padding: 10px 12px !important;
            }
        }

        @media (max-width: 480px) {
            .wrap { margin-right: 5px !important; }
            .broodle-page-header { padding: 12px 0 !important; }
            .broodle-page-title { font-size: 18px !important; }
            .template-header-cell, .template-content-cell { padding: 14px !important; }
            .variables-accordion-toggle { font-size: 15px !important; }
            .variable-select-label { font-size: 12px !important; }
        }
            .variables-accordion-toggle { padding: 12px 0 !important; font-size: 15px !important; }
        }
        </style>
        <form method="post" action="options.php">
            <?php
            settings_fields( 'broodle_wa_settings_group' );
            wp_nonce_field( 'broodle_wa_settings_nonce', 'broodle_wa_settings_nonce' );
            ?>

            <div class="broodle-page-header">
                <h2 class="broodle-page-title">
                    <span class="dashicons dashicons-whatsapp"></span>
                    <?php esc_html_e( 'WhatsApp Template Configuration', 'broodle-wa-connector' ); ?>
                </h2>
                <p class="broodle-page-description"><?php esc_html_e( 'Configure WhatsApp templates for each order status. Templates must be pre-approved in your Broodle dashboard.', 'broodle-wa-connector' ); ?></p>
            </div>

            <table class="form-table template-config-table">
                <?php foreach ( Broodle_WA_Settings::get_order_status_options() as $status => $label ) : ?>
                    <tr class="template-row-wrapper">
                        <th scope="row" class="template-header-cell">
                            <div class="template-header-content">
                                <h3 class="template-title"><?php echo esc_html( $label ); ?></h3>
                                <label class="template-toggle-wrapper">
                                    <input type="checkbox" name="broodle_wa_settings[enabled_notifications][<?php echo esc_attr( $status ); ?>]" value="yes" <?php checked( $settings['enabled_notifications'][ $status ] ?? 'no', 'yes' ); ?> class="template-checkbox" />
                                    <span class="template-toggle-slider"></span>
                                    <span class="toggle-label"><?php esc_html_e( 'Enable', 'broodle-wa-connector' ); ?></span>
                                </label>
                            </div>
                        </th>
                        <td class="template-content-cell">
                            <div class="template-main-settings">
                                <div class="template-name-group">
                                    <label for="template_<?php echo esc_attr( $status ); ?>" class="template-name-label">
                                        <?php esc_html_e( 'Template Name:', 'broodle-wa-connector' ); ?>
                                    </label>
                                    <div class="template-name-input-group">
                                        <input type="text"
                                               id="template_<?php echo esc_attr( $status ); ?>"
                                               name="broodle_wa_settings[templates][<?php echo esc_attr( $status ); ?>]"
                                               value="<?php echo esc_attr( $settings['templates'][ $status ] ?? '' ); ?>"
                                               class="regular-text template-name-input"
                                               placeholder="<?php esc_attr_e( 'Enter template name', 'broodle-wa-connector' ); ?>" />
                                        <button type="button" class="button button-secondary send-test-message" data-status="<?php echo esc_attr( $status ); ?>">
                                            <?php esc_html_e( 'Send Test', 'broodle-wa-connector' ); ?>
                                        </button>
                                    </div>
                                </div>

                                <?php if ( in_array( $status, array( 'order_shipped', 'order_delivered' ), true ) ) : ?>
                                    <div class="template-status-mapping">
                                        <label for="status_mapping_<?php echo esc_attr( $status ); ?>" class="template-name-label">
                                            <?php esc_html_e( 'Trigger on Status:', 'broodle-wa-connector' ); ?>
                                        </label>
                                        <select name="broodle_wa_settings[status_mapping][<?php echo esc_attr( $status ); ?>]"
                                                id="status_mapping_<?php echo esc_attr( $status ); ?>"
                                                class="regular-text status-mapping-select">
                                            <option value=""><?php esc_html_e( 'Select Order Status', 'broodle-wa-connector' ); ?></option>
                                            <?php
                                            $current_mapping = $settings['status_mapping'][ $status ] ?? '';
                                            $wc_statuses = $this->get_woocommerce_order_statuses();
                                            foreach ( $wc_statuses as $wc_status => $wc_label ) :
                                            ?>
                                                <option value="<?php echo esc_attr( $wc_status ); ?>" <?php selected( $current_mapping, $wc_status ); ?>>
                                                    <?php echo esc_html( $wc_label ); ?> (<?php echo esc_html( $wc_status ); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <p class="description">
                                            <?php esc_html_e( 'Select which WooCommerce order status should trigger this notification.', 'broodle-wa-connector' ); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>

                                <?php if ( in_array( $status, array( 'order_failed', 'order_cancelled', 'abandoned_cart_recovery' ), true ) ) : ?>
                                    <div class="template-image-selection">
                                        <label for="template_image_<?php echo esc_attr( $status ); ?>" class="template-name-label">
                                            <?php esc_html_e( 'Attach Image:', 'broodle-wa-connector' ); ?>
                                        </label>
                                        <div class="template-image-input-group">
                                            <?php
                                            $current_image_id = $settings['template_images'][ $status ] ?? '';
                                            $image_url = '';
                                            $image_filename = '';
                                            if ( $current_image_id ) {
                                                $image_url = wp_get_attachment_image_url( $current_image_id, 'thumbnail' );
                                                $image_filename = basename( get_attached_file( $current_image_id ) );
                                            }
                                            ?>
                                            <input type="hidden"
                                                   name="broodle_wa_settings[template_images][<?php echo esc_attr( $status ); ?>]"
                                                   id="template_image_<?php echo esc_attr( $status ); ?>"
                                                   value="<?php echo esc_attr( $current_image_id ); ?>" />

                                            <div class="template-image-preview" id="image_preview_<?php echo esc_attr( $status ); ?>">
                                                <?php if ( $image_url ) : ?>
                                                    <img src="<?php echo esc_url( $image_url ); ?>" alt="Selected image" style="max-width: 100px; max-height: 100px; border-radius: 4px;" />
                                                    <p class="image-filename"><?php echo esc_html( $image_filename ); ?></p>
                                                <?php else : ?>
                                                    <div class="no-image-placeholder">
                                                        <span class="dashicons dashicons-format-image"></span>
                                                        <p><?php esc_html_e( 'No image selected', 'broodle-wa-connector' ); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="template-image-buttons">
                                                <button type="button"
                                                        class="button button-secondary select-image-btn"
                                                        data-status="<?php echo esc_attr( $status ); ?>">
                                                    <?php esc_html_e( 'Select Image', 'broodle-wa-connector' ); ?>
                                                </button>
                                                <button type="button"
                                                        class="button button-link-delete remove-image-btn"
                                                        data-status="<?php echo esc_attr( $status ); ?>"
                                                        <?php echo $current_image_id ? '' : 'style="display:none;"'; ?>>
                                                    <?php esc_html_e( 'Remove', 'broodle-wa-connector' ); ?>
                                                </button>
                                            </div>
                                        </div>
                                        <p class="description">
                                            <?php esc_html_e( 'Optional: Select an image to send with this notification. Image will be attached to the WhatsApp message.', 'broodle-wa-connector' ); ?>
                                        </p>
                                    </div>

                                    <div class="template-coupon-code">
                                        <label for="template_coupon_<?php echo esc_attr( $status ); ?>" class="template-name-label">
                                            <?php esc_html_e( 'Coupon Code:', 'broodle-wa-connector' ); ?>
                                        </label>
                                        <div class="template-coupon-input-group">
                                            <input type="text"
                                                   name="broodle_wa_settings[template_coupon_codes][<?php echo esc_attr( $status ); ?>]"
                                                   id="template_coupon_<?php echo esc_attr( $status ); ?>"
                                                   value="<?php echo esc_attr( $settings['template_coupon_codes'][ $status ] ?? '' ); ?>"
                                                   class="regular-text template-coupon-input"
                                                   placeholder="<?php esc_attr_e( 'Enter coupon code (e.g., SAVE20)', 'broodle-wa-connector' ); ?>" />

                                            <select name="broodle_wa_settings[template_coupon_positions][<?php echo esc_attr( $status ); ?>]"
                                                    id="template_coupon_position_<?php echo esc_attr( $status ); ?>"
                                                    class="template-coupon-position-select">
                                                <option value=""><?php esc_html_e( 'Select Position', 'broodle-wa-connector' ); ?></option>
                                                <?php
                                                $current_position = $settings['template_coupon_positions'][ $status ] ?? '1';
                                                for ( $i = 1; $i <= 5; $i++ ) :
                                                ?>
                                                    <option value="<?php echo esc_attr( $i ); ?>" <?php selected( $current_position, $i ); ?>>
                                                        <?php printf( esc_html__( 'Variable %d Position', 'broodle-wa-connector' ), $i ); ?>
                                                    </option>
                                                <?php endfor; ?>
                                            </select>
                                        </div>
                                        <p class="description">
                                            <?php esc_html_e( 'Optional: Enter a coupon code and select which variable position it should occupy in the WhatsApp template.', 'broodle-wa-connector' ); ?>
                                        </p>
                                    </div>

                                    <div class="template-delay">
                                        <label for="template_delay_<?php echo esc_attr( $status ); ?>" class="template-name-label">
                                            <?php esc_html_e( 'Send After:', 'broodle-wa-connector' ); ?>
                                        </label>
                                        <select name="broodle_wa_settings[template_delays][<?php echo esc_attr( $status ); ?>]"
                                                id="template_delay_<?php echo esc_attr( $status ); ?>"
                                                class="template-delay-select">
                                            <?php
                                            $current_delay = $settings['template_delays'][ $status ] ?? '0';
                                            $delay_options = array(
                                                '0' => __( 'Immediately', 'broodle-wa-connector' ),
                                                '5' => __( '5 minutes', 'broodle-wa-connector' ),
                                                '10' => __( '10 minutes', 'broodle-wa-connector' ),
                                                '15' => __( '15 minutes', 'broodle-wa-connector' ),
                                                '20' => __( '20 minutes', 'broodle-wa-connector' ),
                                                '30' => __( '30 minutes', 'broodle-wa-connector' ),
                                                '45' => __( '45 minutes', 'broodle-wa-connector' ),
                                                '60' => __( '1 hour', 'broodle-wa-connector' ),
                                                '120' => __( '2 hours', 'broodle-wa-connector' ),
                                                '180' => __( '3 hours', 'broodle-wa-connector' ),
                                                '360' => __( '6 hours', 'broodle-wa-connector' ),
                                                '720' => __( '12 hours', 'broodle-wa-connector' ),
                                                '1440' => __( '24 hours', 'broodle-wa-connector' ),
                                            );
                                            foreach ( $delay_options as $value => $label ) :
                                            ?>
                                                <option value="<?php echo esc_attr( $value ); ?>" <?php selected( $current_delay, $value ); ?>>
                                                    <?php echo esc_html( $label ); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <p class="description">
                                            <?php esc_html_e( 'Delay before sending the notification after order status changes. Useful for recovery messages.', 'broodle-wa-connector' ); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="template-variables-wrapper">
                                <button type="button" class="variables-accordion-toggle" data-status="<?php echo esc_attr( $status ); ?>">
                                    <span class="accordion-title"><?php esc_html_e( 'Template Variables', 'broodle-wa-connector' ); ?></span>
                                    <span class="accordion-arrow">▼</span>
                                </button>

                                <div class="variables-accordion-content hidden" id="variables-content-<?php echo esc_attr( $status ); ?>">
                                    <p class="variables-description">
                                        <?php esc_html_e( 'Select up to 5 variables to send with this template. Variables will be sent in the order specified.', 'broodle-wa-connector' ); ?>
                                    </p>

                                    <?php
                                    $template_variables = $settings['template_variables'][ $status ] ?? array( '', '', '', '', '' );
                                    $variable_options = Broodle_WA_Settings::get_template_variable_options();
                                    ?>

                                    <div class="variables-selection-grid">
                                        <?php for ( $i = 1; $i <= 5; $i++ ) : ?>
                                            <div class="variable-select-group">
                                                <label for="variable_<?php echo esc_attr( $status ); ?>_<?php echo $i; ?>" class="variable-select-label">
                                                    <?php printf( esc_html__( 'Variable %d:', 'broodle-wa-connector' ), $i ); ?>
                                                </label>
                                                <select name="broodle_wa_settings[template_variables][<?php echo esc_attr( $status ); ?>][<?php echo $i - 1; ?>]"
                                                        id="variable_<?php echo esc_attr( $status ); ?>_<?php echo $i; ?>"
                                                        class="variable-select">
                                                    <?php foreach ( $variable_options as $value => $option_label ) : ?>
                                                        <option value="<?php echo esc_attr( $value ); ?>" <?php selected( $template_variables[ $i - 1 ] ?? '', $value ); ?>>
                                                            <?php echo esc_html( $option_label ); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>

            <?php submit_button(); ?>
        </form>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find all accordion toggles
            var toggles = document.querySelectorAll('.variables-accordion-toggle');

            // Hide all content initially
            var contents = document.querySelectorAll('.variables-accordion-content');
            for (var i = 0; i < contents.length; i++) {
                contents[i].classList.add('hidden');
                contents[i].classList.remove('visible');
            }

            // Add click handlers
            for (var j = 0; j < toggles.length; j++) {
                toggles[j].addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    var button = this;
                    var status = button.getAttribute('data-status');
                    var contentId = 'variables-content-' + status;
                    var content = document.getElementById(contentId);
                    var isActive = button.classList.contains('active');

                    if (!content) return;

                    if (isActive) {
                        button.classList.remove('active');
                        content.classList.add('hidden');
                        content.classList.remove('visible');
                    } else {
                        button.classList.add('active');
                        content.classList.remove('hidden');
                        content.classList.add('visible');
                    }
                });
            }
        });

        // Media Library functionality for image selection
        jQuery(document).ready(function($) {
            var mediaFrame;



            // Media functionality ends here

            // Handle select image button click
            $('.select-image-btn').on('click', function(e) {
                e.preventDefault();

                var button = $(this);
                var status = button.data('status');

                // Create media frame if it doesn't exist
                if (mediaFrame) {
                    mediaFrame.close();
                }

                mediaFrame = wp.media({
                    title: 'Select Image for ' + status.replace('order_', '').replace('_', ' ').toUpperCase() + ' Notification',
                    button: {
                        text: 'Use This Image'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });

                // Handle image selection
                mediaFrame.on('select', function() {
                    var attachment = mediaFrame.state().get('selection').first().toJSON();

                    // Update hidden input
                    $('#template_image_' + status).val(attachment.id);

                    // Update preview
                    var preview = $('#image_preview_' + status);
                    var thumbnailUrl = attachment.sizes && attachment.sizes.thumbnail ?
                                     attachment.sizes.thumbnail.url : attachment.url;

                    preview.html(
                        '<img src="' + thumbnailUrl + '" alt="Selected image" style="max-width: 100px; max-height: 100px; border-radius: 4px; object-fit: cover;" />' +
                        '<p class="image-filename">' + attachment.filename + '</p>'
                    );

                    // Show remove button
                    $('.remove-image-btn[data-status="' + status + '"]').show();
                });

                // Open media frame
                mediaFrame.open();
            });

            // Handle remove image button click
            $('.remove-image-btn').on('click', function(e) {
                e.preventDefault();

                var button = $(this);
                var status = button.data('status');

                // Clear hidden input
                $('#template_image_' + status).val('');

                // Reset preview
                var preview = $('#image_preview_' + status);
                preview.html(
                    '<div class="no-image-placeholder">' +
                    '<span class="dashicons dashicons-format-image"></span>' +
                    '<p>No image selected</p>' +
                    '</div>'
                );

                // Hide remove button
                button.hide();
            });
        });
        </script>
        <?php
    }

    /**
     * Get WooCommerce order statuses
     *
     * @return array
     */
    private function get_woocommerce_order_statuses() {
        if ( ! function_exists( 'wc_get_order_statuses' ) ) {
            return array();
        }

        $statuses = wc_get_order_statuses();

        // Remove the 'wc-' prefix for cleaner display and add some custom options
        $clean_statuses = array();
        foreach ( $statuses as $key => $label ) {
            $clean_key = str_replace( 'wc-', '', $key );
            $clean_statuses[ $clean_key ] = sprintf( '%s (%s)', $label, $clean_key );
        }

        // Add common shipping plugin statuses that might not be registered yet
        $additional_statuses = array(
            'shipped' => 'Shipped (shipped)',
            'partial-shipped' => 'Partially Shipped (partial-shipped)',
            'delivered' => 'Delivered (delivered)',
            'out-for-delivery' => 'Out for Delivery (out-for-delivery)',
            'dispatched' => 'Dispatched (dispatched)',
            'in-transit' => 'In Transit (in-transit)',
            'ready-for-pickup' => 'Ready for Pickup (ready-for-pickup)',
            'picked-up' => 'Picked Up (picked-up)',
        );

        // Only add if they don't already exist
        foreach ( $additional_statuses as $key => $label ) {
            if ( ! isset( $clean_statuses[ $key ] ) ) {
                $clean_statuses[ $key ] = $label;
            }
        }

        return $clean_statuses;
    }

    /**
     * Render logs tab
     */
    private function render_logs_tab() {
        $page = isset( $_GET['paged'] ) ? max( 1, absint( $_GET['paged'] ) ) : 1;
        $per_page = 20;
        $offset = ( $page - 1 ) * $per_page;

        $logs_data = Broodle_WA_Logger::get_logs(
            array(
                'limit' => $per_page,
                'offset' => $offset,
            )
        );

        $stats = Broodle_WA_Logger::get_stats( 30 );
        $scheduled_logs = Broodle_WA_Logger::get_scheduled_logs();
        ?>
        <style>
        /* Logs Page Layout Optimization */
        .wrap { max-width: none !important; margin-right: 20px !important; }
        .tab-content { max-width: none !important; }

        /* Logs Page Styling */
        .logs-page-header { margin-bottom: 30px !important; padding: 20px 0 !important; border-bottom: 1px solid #e1e5e9 !important; }
        .logs-page-title { margin: 0 0 12px 0 !important; font-size: 24px !important; font-weight: 600 !important; color: #1d2327 !important; display: flex !important; align-items: center !important; gap: 12px !important; }
        .logs-page-title .dashicons { font-size: 28px !important; width: 28px !important; height: 28px !important; color: #25d366 !important; }

        /* Enhanced Stats Grid - Optimized for 3 boxes */
        .logs-stats-grid {
            display: grid !important;
            grid-template-columns: repeat(3, 1fr) !important;
            gap: 24px !important;
            margin-bottom: 40px !important;
            max-width: 900px !important;
        }
        .logs-stat-box {
            background: #fff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            padding: 24px !important;
            text-align: center !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            transition: transform 0.2s ease, box-shadow 0.2s ease !important;
        }
        .logs-stat-box:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        }
        .logs-stat-box h3 {
            font-size: 2.8em !important;
            margin: 0 0 12px 0 !important;
            font-weight: 700 !important;
            line-height: 1 !important;
        }
        .logs-stat-box p {
            margin: 0 !important;
            color: #646970 !important;
            font-weight: 500 !important;
            font-size: 14px !important;
        }
        .logs-stat-box.success h3 { color: #00a32a !important; }
        .logs-stat-box.error h3 { color: #d63638 !important; }
        .logs-stat-box.pending h3 { color: #dba617 !important; }

        /* Section Headers */
        .logs-section-header {
            display: flex !important;
            align-items: center !important;
            justify-content: space-between !important;
            margin: 40px 0 20px 0 !important;
            padding-bottom: 12px !important;
            border-bottom: 2px solid #f0f0f1 !important;
        }
        .logs-section-title {
            font-size: 20px !important;
            font-weight: 600 !important;
            color: #1d2327 !important;
            margin: 0 !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }
        .logs-section-count {
            background: #f0f0f1 !important;
            color: #646970 !important;
            padding: 4px 12px !important;
            border-radius: 12px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
        }

        /* Enhanced Table Styling - Optimized for Full Width */
        .logs-table-container {
            background: #fff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            margin-bottom: 30px !important;
            width: 100% !important;
        }
        .logs-table {
            margin: 0 !important;
            border: none !important;
            border-radius: 0 !important;
            width: 100% !important;
            table-layout: fixed !important;
        }
        .logs-table thead th {
            background: #f8f9fa !important;
            border-bottom: 2px solid #e1e5e9 !important;
            padding: 16px 12px !important;
            font-weight: 600 !important;
            color: #1d2327 !important;
            font-size: 13px !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
        }
        .logs-table tbody td {
            padding: 14px 12px !important;
            border-bottom: 1px solid #f0f0f1 !important;
            vertical-align: top !important;
            font-size: 13px !important;
            word-wrap: break-word !important;
        }
        .logs-table tbody tr:hover {
            background: #f8f9fa !important;
        }
        .logs-table tbody tr:last-child td {
            border-bottom: none !important;
        }

        /* Column Width Optimization */
        .logs-table th:nth-child(1), .logs-table td:nth-child(1) { width: 15% !important; } /* Date */
        .logs-table th:nth-child(2), .logs-table td:nth-child(2) { width: 10% !important; } /* Order ID */
        .logs-table th:nth-child(3), .logs-table td:nth-child(3) { width: 15% !important; } /* Phone */
        .logs-table th:nth-child(4), .logs-table td:nth-child(4) { width: 15% !important; } /* Template */
        .logs-table th:nth-child(5), .logs-table td:nth-child(5) { width: 10% !important; } /* Status */
        .logs-table th:nth-child(6), .logs-table td:nth-child(6) { width: 25% !important; } /* API Response */
        .logs-table th:nth-child(7), .logs-table td:nth-child(7) { width: 10% !important; } /* Error */

        /* Scheduled Table Column Widths */
        .logs-table.scheduled th:nth-child(1), .logs-table.scheduled td:nth-child(1) { width: 20% !important; } /* Scheduled Time */
        .logs-table.scheduled th:nth-child(2), .logs-table.scheduled td:nth-child(2) { width: 12% !important; } /* Order ID */
        .logs-table.scheduled th:nth-child(3), .logs-table.scheduled td:nth-child(3) { width: 18% !important; } /* Phone */
        .logs-table.scheduled th:nth-child(4), .logs-table.scheduled td:nth-child(4) { width: 20% !important; } /* Template */
        .logs-table.scheduled th:nth-child(5), .logs-table.scheduled td:nth-child(5) { width: 15% !important; } /* Status */
        .logs-table.scheduled th:nth-child(6), .logs-table.scheduled td:nth-child(6) { width: 15% !important; } /* Delay */

        /* Status Badge Improvements */
        .logs-status-badge {
            display: inline-block !important;
            padding: 6px 12px !important;
            border-radius: 16px !important;
            font-size: 11px !important;
            font-weight: 600 !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            color: #fff !important;
        }
        .logs-status-badge.status-success { background: linear-gradient(135deg, #00a32a, #00ba37) !important; }
        .logs-status-badge.status-error { background: linear-gradient(135deg, #d63638, #dc3545) !important; }
        .logs-status-badge.status-pending { background: linear-gradient(135deg, #dba617, #f0b90b) !important; color: #1d2327 !important; }
        .logs-status-badge.status-retry { background: linear-gradient(135deg, #0073aa, #005a87) !important; }

        /* Order Link Styling */
        .logs-order-link {
            color: #2271b1 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            transition: background-color 0.2s ease !important;
        }
        .logs-order-link:hover {
            background: #f0f6fc !important;
            color: #135e96 !important;
        }

        /* API Response Details */
        .logs-api-response {
            cursor: pointer !important;
        }
        .logs-api-response summary {
            font-weight: 500 !important;
            color: #2271b1 !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            transition: background-color 0.2s ease !important;
        }
        .logs-api-response summary:hover {
            background: #f0f6fc !important;
        }
        .logs-api-response pre {
            background: #f8f9fa !important;
            border: 1px solid #e1e5e9 !important;
            padding: 12px !important;
            margin: 8px 0 !important;
            border-radius: 4px !important;
            font-size: 11px !important;
            max-height: 200px !important;
            overflow-y: auto !important;
            line-height: 1.4 !important;
        }

        /* Scheduled Notifications Special Styling */
        .scheduled-notifications-container {
            background: #fff9e6 !important;
            border: 1px solid #f0e68c !important;
            border-radius: 8px !important;
            padding: 20px !important;
            margin-bottom: 30px !important;
        }

        /* Two-column layout for larger screens */
        .logs-content-wrapper {
            display: grid !important;
            grid-template-columns: 1fr !important;
            gap: 30px !important;
        }

        @media (min-width: 1400px) {
            .logs-content-wrapper.has-scheduled {
                grid-template-columns: 1fr 1fr !important;
                gap: 40px !important;
            }
            .logs-content-wrapper.has-scheduled .scheduled-section {
                order: 1 !important;
            }
            .logs-content-wrapper.has-scheduled .recent-logs-section {
                order: 2 !important;
            }
        }
        .scheduled-notifications-description {
            margin: 0 0 16px 0 !important;
            color: #8a6914 !important;
            font-size: 14px !important;
            display: flex !important;
            align-items: center !important;
            gap: 8px !important;
        }
        .overdue-notification {
            background: #fff2f2 !important;
        }
        .overdue-notification td {
            border-left: 4px solid #d63638 !important;
        }

        /* Pagination Improvements */
        .logs-pagination {
            background: #fff !important;
            border: 1px solid #e1e5e9 !important;
            border-radius: 8px !important;
            padding: 16px 20px !important;
            margin-top: 20px !important;
        }
        .logs-pagination .tablenav-pages {
            margin: 0 !important;
        }
        .logs-pagination .page-numbers {
            padding: 8px 12px !important;
            margin: 0 4px !important;
            border-radius: 4px !important;
            transition: all 0.2s ease !important;
        }
        .logs-pagination .page-numbers:hover {
            background: #f0f6fc !important;
        }
        .logs-pagination .page-numbers.current {
            background: #2271b1 !important;
            color: #fff !important;
        }

        /* Responsive Design for Logs Page */
        @media (max-width: 1200px) {
            .logs-stats-grid {
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 16px !important;
                max-width: none !important;
            }
            .logs-table {
                table-layout: auto !important;
            }
        }

        @media (max-width: 782px) {
            .logs-page-header { padding: 16px 0 !important; margin-bottom: 20px !important; }
            .logs-page-title { font-size: 20px !important; flex-direction: column !important; text-align: center !important; gap: 8px !important; }
            .logs-page-title .dashicons { font-size: 24px !important; }

            .logs-stats-grid {
                grid-template-columns: repeat(3, 1fr) !important;
                gap: 12px !important;
                margin-bottom: 30px !important;
                max-width: none !important;
            }
            .logs-stat-box {
                padding: 16px !important;
            }
            .logs-stat-box h3 {
                font-size: 2.2em !important;
            }

            .logs-section-header {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 8px !important;
                margin: 30px 0 16px 0 !important;
            }
            .logs-section-title {
                font-size: 18px !important;
            }

            .scheduled-notifications-container {
                padding: 16px !important;
            }

            /* Mobile Table Styling */
            .logs-table-container {
                overflow-x: auto !important;
            }
            .logs-table {
                min-width: 600px !important;
            }
            .logs-table thead th {
                padding: 12px 8px !important;
                font-size: 12px !important;
            }
            .logs-table tbody td {
                padding: 12px 8px !important;
                font-size: 12px !important;
            }

            .logs-status-badge {
                padding: 4px 8px !important;
                font-size: 10px !important;
            }

            .logs-api-response summary {
                font-size: 11px !important;
            }
            .logs-api-response pre {
                font-size: 10px !important;
                padding: 8px !important;
            }

            .logs-pagination {
                padding: 12px 16px !important;
            }
        }

        @media (max-width: 600px) {
            .logs-stats-grid {
                grid-template-columns: 1fr !important;
                gap: 10px !important;
            }
            .logs-stat-box {
                padding: 12px !important;
            }
            .logs-stat-box h3 {
                font-size: 1.8em !important;
            }
            .logs-stat-box p {
                font-size: 12px !important;
            }
        }

        @media (max-width: 480px) {
            .wrap { margin-right: 10px !important; }
            .logs-page-title {
                font-size: 18px !important;
            }
            .logs-stats-grid {
                grid-template-columns: 1fr !important;
                gap: 8px !important;
            }
            .logs-section-title {
                font-size: 16px !important;
            }
            .scheduled-notifications-container {
                padding: 12px !important;
            }
            .logs-table {
                min-width: 500px !important;
            }
        }
        </style>

        <div class="logs-page-header">
            <h2 class="logs-page-title">
                <span class="dashicons dashicons-chart-line"></span>
                <?php esc_html_e( 'Notification Statistics (Last 30 Days)', 'broodle-wa-connector' ); ?>
            </h2>
        </div>

        <div class="logs-stats-grid">
            <div class="logs-stat-box">
                <h3><?php echo esc_html( $stats['total'] ); ?></h3>
                <p><?php esc_html_e( 'Total Notifications', 'broodle-wa-connector' ); ?></p>
            </div>
            <div class="logs-stat-box success">
                <h3><?php echo esc_html( $stats['success'] ); ?></h3>
                <p><?php esc_html_e( 'Successful', 'broodle-wa-connector' ); ?></p>
            </div>
            <div class="logs-stat-box pending">
                <h3><?php echo esc_html( $stats['pending'] ); ?></h3>
                <p><?php esc_html_e( 'Pending', 'broodle-wa-connector' ); ?></p>
            </div>
        </div>

        <div class="logs-content-wrapper <?php echo ! empty( $scheduled_logs['logs'] ) ? 'has-scheduled' : ''; ?>">
            <?php if ( ! empty( $scheduled_logs['logs'] ) ) : ?>
                <div class="scheduled-section">
            <div class="logs-section-header">
                <h2 class="logs-section-title">
                    <span class="dashicons dashicons-clock"></span>
                    <?php esc_html_e( 'Scheduled Notifications', 'broodle-wa-connector' ); ?>
                </h2>
                <span class="logs-section-count"><?php echo count( $scheduled_logs['logs'] ); ?></span>
            </div>
            <div class="scheduled-notifications-container">
                <p class="scheduled-notifications-description">
                    <span class="dashicons dashicons-info"></span>
                    <?php esc_html_e( 'These notifications are scheduled to be sent at the specified times:', 'broodle-wa-connector' ); ?>
                </p>
                <div class="logs-table-container">
                    <table class="wp-list-table widefat fixed striped logs-table scheduled">
                    <thead>
                        <tr>
                            <th><?php esc_html_e( 'Scheduled Time', 'broodle-wa-connector' ); ?></th>
                            <th><?php esc_html_e( 'Order ID', 'broodle-wa-connector' ); ?></th>
                            <th><?php esc_html_e( 'Phone', 'broodle-wa-connector' ); ?></th>
                            <th><?php esc_html_e( 'Template', 'broodle-wa-connector' ); ?></th>
                            <th><?php esc_html_e( 'Status', 'broodle-wa-connector' ); ?></th>
                            <th><?php esc_html_e( 'Delay', 'broodle-wa-connector' ); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ( $scheduled_logs['logs'] as $log ) :
                            $response_data = json_decode( $log->api_response, true );
                            $scheduled_time = $response_data['scheduled_time'] ?? '';
                            $delay_minutes = $response_data['delay_minutes'] ?? '';
                            $is_overdue = strtotime( $scheduled_time ) < time();
                        ?>
                            <tr class="<?php echo $is_overdue ? 'overdue-notification' : ''; ?>">
                                <td>
                                    <strong><?php echo esc_html( $scheduled_time ); ?></strong>
                                    <?php if ( $is_overdue ) : ?>
                                        <br><span style="color: #d63638; font-size: 11px;">⚠️ OVERDUE</span>
                                    <?php else : ?>
                                        <br><span style="color: #646970; font-size: 11px;">
                                            <?php
                                            $time_diff = strtotime( $scheduled_time ) - time();
                                            if ( $time_diff > 0 ) {
                                                echo sprintf( 'in %s', human_time_diff( time(), strtotime( $scheduled_time ) ) );
                                            }
                                            ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo esc_url( admin_url( 'post.php?post=' . $log->order_id . '&action=edit' ) ); ?>" class="logs-order-link">
                                        #<?php echo esc_html( $log->order_id ); ?>
                                    </a>
                                </td>
                                <td><span class="logs-phone-number"><?php echo esc_html( $log->phone_number ); ?></span></td>
                                <td><span class="logs-template-name"><?php echo esc_html( $log->template_name ); ?></span></td>
                                <td>
                                    <span class="logs-status-badge status-<?php echo esc_attr( $log->status ); ?>">
                                        <?php echo esc_html( ucfirst( $log->status ) ); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html( $delay_minutes ); ?> minutes</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    </table>
                </div>
            </div>
                </div>
            <?php endif; ?>

            <div class="recent-logs-section">
                <div class="logs-section-header">
            <h2 class="logs-section-title">
                <span class="dashicons dashicons-list-view"></span>
                <?php esc_html_e( 'Recent Notification Logs', 'broodle-wa-connector' ); ?>
            </h2>
        </div>
        <?php if ( ! empty( $logs_data['logs'] ) ) : ?>
            <div class="logs-table-container">
                <table class="wp-list-table widefat fixed striped logs-table">
                <thead>
                    <tr>
                        <th><?php esc_html_e( 'Date', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'Order ID', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'Phone', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'Template', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'Status', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'API Response', 'broodle-wa-connector' ); ?></th>
                        <th><?php esc_html_e( 'Error', 'broodle-wa-connector' ); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ( $logs_data['logs'] as $log ) : ?>
                        <tr>
                            <td><?php echo esc_html( $log->created_at ); ?></td>
                            <td>
                                <a href="<?php echo esc_url( admin_url( 'post.php?post=' . $log->order_id . '&action=edit' ) ); ?>" class="logs-order-link">
                                    #<?php echo esc_html( $log->order_id ); ?>
                                </a>
                            </td>
                            <td><span class="logs-phone-number"><?php echo esc_html( $log->phone_number ); ?></span></td>
                            <td><span class="logs-template-name"><?php echo esc_html( $log->template_name ); ?></span></td>
                            <td>
                                <span class="logs-status-badge status-<?php echo esc_attr( $log->status ); ?>">
                                    <?php echo esc_html( ucfirst( $log->status ) ); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ( ! empty( $log->api_response ) ) : ?>
                                    <?php
                                    $api_response = json_decode( $log->api_response, true );
                                    if ( $api_response ) {
                                        echo '<details class="logs-api-response">';
                                        echo '<summary>View Response</summary>';
                                        echo '<pre>';
                                        echo esc_html( wp_json_encode( $api_response, JSON_PRETTY_PRINT ) );
                                        echo '</pre>';
                                        echo '</details>';
                                    } else {
                                        echo '<span style="color: #666; font-style: italic;">Invalid JSON</span>';
                                    }
                                    ?>
                                <?php else : ?>
                                    <span style="color: #999;">—</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ( ! empty( $log->error_message ) ) : ?>
                                    <span class="logs-error-message"><?php echo esc_html( $log->error_message ); ?></span>
                                <?php else : ?>
                                    <span style="color: #999;">—</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                </table>
            </div>

            <?php
            // Pagination
            $total_pages = ceil( $logs_data['total'] / $per_page );
            if ( $total_pages > 1 ) {
                echo '<div class="logs-pagination"><div class="tablenav-pages">';
                echo paginate_links(
                    array(
                        'base' => add_query_arg( 'paged', '%#%' ),
                        'format' => '',
                        'prev_text' => __( '&laquo; Previous' ),
                        'next_text' => __( 'Next &raquo;' ),
                        'total' => $total_pages,
                        'current' => $page,
                    )
                );
                echo '</div></div>';
            }
            ?>
        <?php else : ?>
            <div class="logs-empty-state">
                <span class="dashicons dashicons-email-alt"></span>
                <h3><?php esc_html_e( 'No notification logs found', 'broodle-wa-connector' ); ?></h3>
                <p><?php esc_html_e( 'WhatsApp notifications will appear here once they are sent.', 'broodle-wa-connector' ); ?></p>
            </div>
        <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render help tab
     */
    private function render_help_tab() {
        ?>
        <h2><?php esc_html_e( 'Getting Started', 'broodle-wa-connector' ); ?></h2>
        <div class="help-section">
            <h3><?php esc_html_e( '1. Register for Broodle WhatsApp API', 'broodle-wa-connector' ); ?></h3>
            <p>
                <?php
                printf(
                    /* translators: %s: Registration URL */
                    esc_html__( 'Visit %s to create your account and get your API credentials.', 'broodle-wa-connector' ),
                    '<a href="https://wa.broodle.one" target="_blank">https://wa.broodle.one</a>'
                );
                ?>
            </p>

            <h3><?php esc_html_e( '2. Create WhatsApp Templates', 'broodle-wa-connector' ); ?></h3>
            <p><?php esc_html_e( 'Create message templates in your Broodle dashboard and get them approved by WhatsApp.', 'broodle-wa-connector' ); ?></p>

            <h3><?php esc_html_e( '3. Configure Plugin Settings', 'broodle-wa-connector' ); ?></h3>
            <p><?php esc_html_e( 'Enter your API credentials and configure your templates for each order status.', 'broodle-wa-connector' ); ?></p>

            <h3><?php esc_html_e( '4. Test Your Setup', 'broodle-wa-connector' ); ?></h3>
            <p><?php esc_html_e( 'Use the test buttons to verify your API connection and template configuration.', 'broodle-wa-connector' ); ?></p>
        </div>

        <h2><?php esc_html_e( 'Debug Information', 'broodle-wa-connector' ); ?></h2>
        <div class="help-section">
            <p><?php esc_html_e( 'If messages are showing as "success" but not being delivered, check the following:', 'broodle-wa-connector' ); ?></p>
            <ul>
                <li><strong><?php esc_html_e( 'Template Name:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Make sure "order_confirmation" template exists in your Broodle account', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Template Status:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Template must be APPROVED by WhatsApp', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Phone Number:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Must be a valid WhatsApp number with country code', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Variables:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Template must expect exactly 3 variables in the correct order', 'broodle-wa-connector' ); ?></li>
            </ul>
            <p><strong><?php esc_html_e( 'Debug Logs:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Check your WordPress error logs for detailed API request/response information.', 'broodle-wa-connector' ); ?></p>
            <p><em><?php esc_html_e( 'Location: wp-content/debug.log (if WP_DEBUG_LOG is enabled)', 'broodle-wa-connector' ); ?></em></p>
        </div>

        <h2><?php esc_html_e( 'Template Variables', 'broodle-wa-connector' ); ?></h2>
        <div class="help-section">
            <p><?php esc_html_e( 'The plugin automatically sends the following variables to your WhatsApp templates in this order:', 'broodle-wa-connector' ); ?></p>
            <ul>
                <li><strong><?php esc_html_e( 'Variable 1:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Customer Full Name (First + Last Name)', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Variable 2:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Order ID/Number', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Variable 3:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Order Total Amount (clean text, no HTML)', 'broodle-wa-connector' ); ?></li>
            </ul>
            <p><strong><?php esc_html_e( 'Additional Variables (for specific order types):', 'broodle-wa-connector' ); ?></strong></p>
            <ul>
                <li><strong><?php esc_html_e( 'Shipped Orders:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Shipping method, Tracking number (if available)', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Delivered Orders:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Delivery date', 'broodle-wa-connector' ); ?></li>
                <li><strong><?php esc_html_e( 'Cancelled/Failed Orders:', 'broodle-wa-connector' ); ?></strong> <?php esc_html_e( 'Cancellation reason (if available)', 'broodle-wa-connector' ); ?></li>
            </ul>
            <p><em><?php esc_html_e( 'Note: Variables are sent in the exact order listed above. Make sure your WhatsApp templates are configured to receive variables in this sequence.', 'broodle-wa-connector' ); ?></em></p>
        </div>

        <h2><?php esc_html_e( 'Support', 'broodle-wa-connector' ); ?></h2>
        <div class="help-section">
            <p>
                <?php
                printf(
                    /* translators: %s: Support URL */
                    esc_html__( 'For support and documentation, visit %s', 'broodle-wa-connector' ),
                    '<a href="https://broodle.host" target="_blank">https://broodle.host</a>'
                );
                ?>
            </p>
        </div>
        <?php
    }

    /**
     * Sanitize settings
     *
     * @param array $input Input settings.
     * @return array
     */
    public function sanitize_settings( $input ) {
        // Verify nonce
        if ( ! isset( $_POST['broodle_wa_settings_nonce'] ) || ! wp_verify_nonce( $_POST['broodle_wa_settings_nonce'], 'broodle_wa_settings_nonce' ) ) {
            add_settings_error( 'broodle_wa_settings', 'nonce_error', __( 'Security check failed.', 'broodle-wa-connector' ) );
            return get_option( 'broodle_wa_settings' );
        }

        $current_settings = Broodle_WA_Settings::get_settings();
        $sanitized = wp_parse_args( $input, $current_settings );

        // Use the settings class sanitization
        return Broodle_WA_Settings::sanitize_settings( $sanitized );
    }

    /**
     * AJAX handler for testing API connection
     */
    public function ajax_test_api() {
        check_ajax_referer( 'broodle_wa_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( __( 'You do not have sufficient permissions to access this page.', 'broodle-wa-connector' ) );
        }

        $api_key = sanitize_text_field( $_POST['api_key'] ?? '' );

        // Test the API connection using the API class
        $api = new Broodle_WA_API( $api_key );
        $result = $api->test_connection();

        if ( is_wp_error( $result ) ) {
            wp_send_json_error( array(
                'message' => $result->get_error_message(),
                'error_code' => $result->get_error_code(),
            ) );
        }

        wp_send_json_success( array(
            'message' => __( 'API connection successful!', 'broodle-wa-connector' ),
            'response_data' => $result,
        ) );
    }

    /**
     * AJAX handler for sending test message
     */
    public function ajax_send_test_message() {
        check_ajax_referer( 'broodle_wa_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( __( 'You do not have sufficient permissions to access this page.', 'broodle-wa-connector' ) );
        }

        $status = sanitize_text_field( $_POST['status'] ?? '' );
        $phone = sanitize_text_field( $_POST['phone'] ?? '' );

        if ( empty( $phone ) ) {
            wp_send_json_error( __( 'Phone number is required for test message.', 'broodle-wa-connector' ) );
        }

        // Send test message logic would go here
        wp_send_json_success( __( 'Test message sent successfully!', 'broodle-wa-connector' ) );
    }

    /**
     * AJAX handler for quick test with predefined values
     */
    public function ajax_quick_test() {
        check_ajax_referer( 'broodle_wa_admin_nonce', 'nonce' );

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( __( 'You do not have sufficient permissions to access this page.', 'broodle-wa-connector' ) );
        }

        $api = new Broodle_WA_API();

        // Test with order_confirmation template and sample variables
        $response = $api->send_template_message(
            '+918769323385',
            'order_confirmation',
            array(
                'Test Customer',  // Variable 1: Full name
                '12345',         // Variable 2: Order ID
                '₹999.00',       // Variable 3: Amount (clean text)
                '---',           // Variable 4: Placeholder for missing data
                '---'            // Variable 5: Placeholder for missing data
            )
        );

        if ( is_wp_error( $response ) ) {
            wp_send_json_error( array(
                'message' => $response->get_error_message(),
                'error_code' => $response->get_error_code(),
            ) );
        }

        // Get detailed response information
        $message = __( 'Quick test message sent successfully to +918769323385!', 'broodle-wa-connector' );
        if ( isset( $response['status_message'] ) && ! empty( $response['status_message'] ) ) {
            $message .= ' ' . $response['status_message'];
        }

        wp_send_json_success( array(
            'message' => $message,
            'response_data' => $response,
        ) );
    }

    /**
     * Test failed/cancelled notification
     */
    public function ajax_test_failed_notification() {
        // Check nonce and permissions
        if ( ! check_ajax_referer( 'broodle_wa_admin_nonce', 'nonce', false ) || ! $this->user_can_access() ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'broodle-wa-connector' ) ) );
        }

        $notification_type = sanitize_text_field( $_POST['notification_type'] ?? 'order_failed' );
        $order_id = intval( $_POST['order_id'] ?? 0 );

        if ( ! $order_id ) {
            wp_send_json_error( array( 'message' => __( 'Please provide a valid order ID.', 'broodle-wa-connector' ) ) );
        }

        $order = wc_get_order( $order_id );
        if ( ! $order ) {
            wp_send_json_error( array( 'message' => __( 'Order not found.', 'broodle-wa-connector' ) ) );
        }

        // Test the notification
        $notifications = new Broodle_WA_Notifications();

        try {
            $notifications->send_notification_safe( $order_id, $notification_type, $order );

            wp_send_json_success( array(
                'message' => "Test {$notification_type} notification sent for order #{$order_id}. Check logs for details.",
            ) );
        } catch ( Exception $e ) {
            wp_send_json_error( array(
                'message' => 'Error: ' . $e->getMessage(),
            ) );
        }
    }

    /**
     * Show diagnostic information about available order statuses
     */
    public function show_status_diagnostic() {
        // Only show on our plugin pages and only if user can manage options
        if ( ! $this->user_can_access() ) {
            return;
        }

        $screen = get_current_screen();
        if ( ! $screen || strpos( $screen->id, 'broodle-wa-connector' ) === false ) {
            return;
        }

        // Only show if diagnostic parameter is present
        if ( ! isset( $_GET['wa_diagnostic'] ) ) {
            return;
        }

        if ( function_exists( 'wc_get_order_statuses' ) ) {
            $statuses = wc_get_order_statuses();

            echo '<div class="notice notice-success is-dismissible">';
            echo '<h3>✅ Order Status Configuration Help</h3>';
            echo '<p><strong>The dropdown now shows both the display name and the actual status key in brackets.</strong></p>';
            echo '<p><strong>Example:</strong> "Completed (completed)" means you select this option to trigger notifications when orders are marked as Completed.</p>';

            echo '<div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #00a32a; margin: 10px 0;">';
            echo '<h4>📋 Available Order Statuses:</h4>';
            echo '<ul style="margin-left: 20px; columns: 2; column-gap: 30px;">';

            foreach ( $statuses as $key => $label ) {
                $clean_key = str_replace( 'wc-', '', $key );
                $icon = '';
                if ( strpos( $clean_key, 'pp-' ) === 0 ) {
                    $icon = '📦 '; // ParcelPanel status
                } elseif ( in_array( $clean_key, array( 'shipped', 'delivered' ) ) ) {
                    $icon = '🚚 '; // Shipping related
                } elseif ( in_array( $clean_key, array( 'completed', 'processing' ) ) ) {
                    $icon = '✅ '; // Standard statuses
                } elseif ( in_array( $clean_key, array( 'cancelled', 'failed' ) ) ) {
                    $icon = '❌ '; // Problem statuses
                }

                echo '<li>' . $icon . '<strong>' . esc_html( $label ) . '</strong> <code>(' . esc_html( $clean_key ) . ')</code></li>';
            }

            echo '</ul>';
            echo '</div>';

            echo '<p><strong>💡 Quick Tips:</strong></p>';
            echo '<ul>';
            echo '<li>For <strong>Order Shipped</strong>: Look for statuses like "Shipped", "Dispatched", or ParcelPanel statuses starting with <code>pp-</code></li>';
            echo '<li>For <strong>Order Delivered</strong>: Look for "Delivered" or "Completed" statuses</li>';
            echo '<li>Test by changing an order to the exact status you selected in the dropdown</li>';
            echo '</ul>';

            echo '<p><a href="' . remove_query_arg( 'wa_diagnostic' ) . '" class="button button-primary">Got it, hide this help</a></p>';
            echo '</div>';
        }
    }

    /**
     * Add dashboard widget
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'broodle_wa_dashboard_widget',
            '📱 Broodle WhatsApp Connector',
            array( $this, 'render_dashboard_widget' ),
            null,
            null,
            'normal',
            'high'
        );
    }

    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        // Get message statistics
        $stats = $this->get_message_statistics();

        ?>
        <div class="broodle-dashboard-widget">
            <style>
            .broodle-dashboard-widget { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }
            .broodle-stats-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0; }
            .broodle-stat-card { background: linear-gradient(135deg, #25d366 0%, #128c7e 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .broodle-stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
            .broodle-stat-label { font-size: 12px; opacity: 0.9; text-transform: uppercase; letter-spacing: 0.5px; }
            .broodle-actions { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; margin-top: 15px; }
            .broodle-action-btn { display: flex; align-items: center; justify-content: center; padding: 12px 15px; text-decoration: none; border-radius: 6px; font-weight: 500; transition: all 0.2s ease; }
            .broodle-action-btn:hover { transform: translateY(-1px); box-shadow: 0 4px 8px rgba(0,0,0,0.15); text-decoration: none; }
            .broodle-btn-primary { background: #0073aa; color: white; }
            .broodle-btn-primary:hover { color: white; }
            .broodle-btn-secondary { background: #f1f1f1; color: #333; border: 1px solid #ddd; }
            .broodle-btn-whatsapp { background: #25d366; color: white; grid-column: 1 / -1; }
            .broodle-btn-whatsapp:hover { color: white; }
            .broodle-status-indicator { display: inline-block; width: 8px; height: 8px; border-radius: 50%; margin-right: 8px; }
            .broodle-status-success { background: #46b450; }
            .broodle-status-warning { background: #ffb900; }
            .broodle-status-error { background: #dc3232; }
            .broodle-widget-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px; }
            .broodle-widget-title { font-size: 16px; font-weight: 600; color: #1d2327; margin: 0; }
            .broodle-widget-status { font-size: 12px; color: #646970; }
            @media (max-width: 782px) {
                .broodle-stats-grid { grid-template-columns: 1fr; }
                .broodle-actions { grid-template-columns: 1fr; }
                .broodle-btn-whatsapp { grid-column: 1; }
            }
            </style>

            <div class="broodle-widget-header">
                <h3 class="broodle-widget-title">Broodle WA API Connection</h3>
                <div class="broodle-widget-status">
                    <span class="broodle-status-indicator broodle-status-<?php echo esc_attr( $stats['status'] ); ?>"></span>
                    <?php echo esc_html( $stats['status_text'] ); ?>
                </div>
            </div>

            <div class="broodle-stats-grid">
                <div class="broodle-stat-card">
                    <div class="broodle-stat-number"><?php echo esc_html( number_format( $stats['today'] ) ); ?></div>
                    <div class="broodle-stat-label">Today</div>
                </div>
                <div class="broodle-stat-card">
                    <div class="broodle-stat-number"><?php echo esc_html( number_format( $stats['last_7_days'] ) ); ?></div>
                    <div class="broodle-stat-label">Last 7 Days</div>
                </div>
                <div class="broodle-stat-card">
                    <div class="broodle-stat-number"><?php echo esc_html( number_format( $stats['last_30_days'] ) ); ?></div>
                    <div class="broodle-stat-label">Last 30 Days</div>
                </div>
            </div>

            <div class="broodle-actions">
                <a href="<?php echo esc_url( admin_url( 'admin.php?page=broodle-wa-connector&tab=logs' ) ); ?>"
                   class="broodle-action-btn broodle-btn-primary">
                    📊 View Logs
                </a>
                <a href="<?php echo esc_url( admin_url( 'admin.php?page=broodle-wa-connector&tab=templates' ) ); ?>"
                   class="broodle-action-btn broodle-btn-secondary">
                    ⚙️ Settings
                </a>
                <a href="https://wa.broodle.one/user?page=inbox"
                   target="_blank"
                   rel="noopener noreferrer"
                   class="broodle-action-btn broodle-btn-whatsapp">
                    💬 Open WhatsApp Inbox
                </a>
            </div>

            <?php if ( $stats['recent_errors'] > 0 ) : ?>
                <div style="margin-top: 15px; padding: 10px; background: #fef7f0; border-left: 4px solid #ffb900; border-radius: 4px;">
                    <strong>⚠️ Notice:</strong> <?php echo esc_html( $stats['recent_errors'] ); ?> failed messages in the last 24 hours.
                    <a href="<?php echo esc_url( admin_url( 'admin.php?page=broodle-wa-connector&tab=logs&status=error' ) ); ?>">View errors</a>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Get message statistics
     *
     * @return array
     */
    private function get_message_statistics() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'broodle_wa_logs';

        // Check if table exists
        if ( $wpdb->get_var( "SHOW TABLES LIKE '{$table_name}'" ) !== $table_name ) {
            return array(
                'today' => 0,
                'last_7_days' => 0,
                'last_30_days' => 0,
                'recent_errors' => 0,
                'status' => 'warning',
                'status_text' => 'Not Configured'
            );
        }

        // Get current date/time
        $now = current_time( 'mysql' );
        $today = current_time( 'Y-m-d' );
        $seven_days_ago = date( 'Y-m-d H:i:s', strtotime( '-7 days', current_time( 'timestamp' ) ) );
        $thirty_days_ago = date( 'Y-m-d H:i:s', strtotime( '-30 days', current_time( 'timestamp' ) ) );
        $twenty_four_hours_ago = date( 'Y-m-d H:i:s', strtotime( '-24 hours', current_time( 'timestamp' ) ) );

        // Get statistics
        $today_count = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE DATE(created_at) = %s AND status = 'success'",
            $today
        ) );

        $seven_days_count = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE created_at >= %s AND status = 'success'",
            $seven_days_ago
        ) );

        $thirty_days_count = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE created_at >= %s AND status = 'success'",
            $thirty_days_ago
        ) );

        $recent_errors = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$table_name} WHERE created_at >= %s AND status = 'error'",
            $twenty_four_hours_ago
        ) );

        // Determine status and status text
        $status = 'success';
        $status_text = 'Connected';

        if ( $recent_errors > 5 ) {
            $status = 'error';
            $status_text = 'Connection Issues';
        } elseif ( $recent_errors > 0 ) {
            $status = 'warning';
            $status_text = 'Partial Issues';
        } elseif ( $today_count == 0 && $seven_days_count == 0 ) {
            $status = 'warning';
            $status_text = 'No Recent Activity';
        }

        return array(
            'today' => intval( $today_count ),
            'last_7_days' => intval( $seven_days_count ),
            'last_30_days' => intval( $thirty_days_count ),
            'recent_errors' => intval( $recent_errors ),
            'status' => $status,
            'status_text' => $status_text
        );
    }
}
