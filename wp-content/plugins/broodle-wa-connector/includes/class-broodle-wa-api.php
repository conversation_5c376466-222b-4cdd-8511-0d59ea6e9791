<?php
/**
 * WhatsApp API client class
 *
 * @package BroodleWAConnector
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * WhatsApp API client class
 */
class Broodle_WA_API {

    /**
     * API base URL
     */
    const API_BASE_URL = 'https://wa.broodle.one/api/v1';

    /**
     * API endpoints
     */
    const ENDPOINT_SEND_TEMPLATE = '/send_templet';

    /**
     * HTTP timeout in seconds
     */
    const HTTP_TIMEOUT = 30;

    /**
     * API key
     *
     * @var string
     */
    private $api_key;

    /**
     * Constructor
     *
     * @param string $api_key API key.
     */
    public function __construct( $api_key = '' ) {
        if ( empty( $api_key ) ) {
            $settings = Broodle_WA_Settings::get_settings();
            $this->api_key = $settings['api_key'];
        } else {
            $this->api_key = $api_key;
        }
    }

    /**
     * Send template message
     *
     * @param string $phone_number Recipient phone number.
     * @param string $template_name Template name.
     * @param array  $template_vars Template variables.
     * @param string $media_uri Optional media URI.
     * @return array|WP_Error
     */
    public function send_template_message( $phone_number, $template_name, $template_vars = array(), $media_uri = '' ) {
        if ( empty( $this->api_key ) ) {
            return new WP_Error( 'missing_credentials', __( 'API key is not configured.', 'broodle-wa-connector' ) );
        }

        if ( empty( $phone_number ) ) {
            return new WP_Error( 'missing_phone', __( 'Phone number is required.', 'broodle-wa-connector' ) );
        }

        if ( empty( $template_name ) ) {
            return new WP_Error( 'missing_template', __( 'Template name is required.', 'broodle-wa-connector' ) );
        }

        // Format phone number
        $formatted_phone = $this->format_phone_number( $phone_number );
        if ( is_wp_error( $formatted_phone ) ) {
            return $formatted_phone;
        }

        // CRITICAL FIX: Validate and clean template variables to prevent default values
        $validated_vars = $this->validate_template_vars( $template_vars );

        // Prepare request data
        $request_data = array(
            'sendTo' => $formatted_phone,
            'templetName' => $template_name,
            'exampleArr' => $validated_vars,
            'token' => $this->api_key,
        );

        // Only add mediaUri if it's a valid URL
        if ( ! empty( $media_uri ) && filter_var( $media_uri, FILTER_VALIDATE_URL ) ) {
            $request_data['mediaUri'] = $media_uri;
        }

        // Make API request
        $response = $this->make_request( self::ENDPOINT_SEND_TEMPLATE, $request_data );

        if ( is_wp_error( $response ) ) {
            return $response;
        }

        // Parse response
        return $this->parse_send_response( $response );
    }

    /**
     * Test API connection
     *
     * @param string $api_key Optional API key for testing.
     * @return bool|WP_Error
     */
    public function test_connection( $api_key = '' ) {
        $test_api_key = ! empty( $api_key ) ? $api_key : $this->api_key;

        if ( empty( $test_api_key ) ) {
            return new WP_Error( 'missing_credentials', __( 'API key is required.', 'broodle-wa-connector' ) );
        }

        // Test connection by making a minimal request to the send template endpoint
        // This will validate the API key without actually sending a message
        $request_data = array(
            'sendTo' => '+1234567890', // Dummy phone number
            'templetName' => 'test_template', // Dummy template
            'exampleArr' => array(), // Empty variables
            'token' => $test_api_key,
        );

        $response = $this->make_request( self::ENDPOINT_SEND_TEMPLATE, $request_data, $test_api_key );

        if ( is_wp_error( $response ) ) {
            // If it's a 401/403 error, it means invalid API key
            $error_message = $response->get_error_message();
            if ( strpos( $error_message, '401' ) !== false || strpos( $error_message, '403' ) !== false ) {
                return new WP_Error( 'invalid_api_key', __( 'Invalid API key.', 'broodle-wa-connector' ) );
            }
            return $response;
        }

        // Even if the template doesn't exist, if we get a proper API response, the connection is working
        // Check for various success indicators or expected error responses
        if ( isset( $response['success'] ) || isset( $response['error'] ) || isset( $response['message'] ) ) {
            return true;
        }

        return new WP_Error( 'connection_failed', __( 'API connection test failed.', 'broodle-wa-connector' ) );
    }

    /**
     * Make HTTP request to API
     *
     * @param string $endpoint API endpoint.
     * @param array  $data Request data.
     * @param string $api_key Optional API key override.
     * @return array|WP_Error
     */
    private function make_request( $endpoint, $data, $api_key = '' ) {
        $url = self::API_BASE_URL . $endpoint;
        $api_key = ! empty( $api_key ) ? $api_key : $this->api_key;

        $headers = array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key,
            'User-Agent' => 'Broodle-WA-Connector/' . BROODLE_WA_VERSION . ' (WordPress)',
        );

        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => wp_json_encode( $data ),
            'timeout' => self::HTTP_TIMEOUT,
            'sslverify' => true,
        );

        // Add filters for customization
        $args = apply_filters( 'broodle_wa_api_request_args', $args, $endpoint, $data );

        $response = wp_remote_request( $url, $args );

        if ( is_wp_error( $response ) ) {
            return new WP_Error(
                'http_request_failed',
                sprintf(
                    /* translators: %s: Error message */
                    __( 'HTTP request failed: %s', 'broodle-wa-connector' ),
                    $response->get_error_message()
                )
            );
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        $response_body = wp_remote_retrieve_body( $response );

        // Log the request for debugging
        $this->log_api_request( $url, $args, $response_code, $response_body );

        if ( $response_code < 200 || $response_code >= 300 ) {
            return new WP_Error(
                'api_error',
                sprintf(
                    /* translators: %1$d: HTTP status code, %2$s: Response body */
                    __( 'API request failed with status %1$d: %2$s', 'broodle-wa-connector' ),
                    $response_code,
                    $response_body
                )
            );
        }

        $decoded_response = json_decode( $response_body, true );

        if ( null === $decoded_response ) {
            return new WP_Error(
                'invalid_response',
                sprintf(
                    /* translators: %s: Raw response body */
                    __( 'Invalid JSON response from API. Raw response: %s', 'broodle-wa-connector' ),
                    substr( $response_body, 0, 200 ) . ( strlen( $response_body ) > 200 ? '...' : '' )
                )
            );
        }

        return $decoded_response;
    }

    /**
     * Parse send template response
     *
     * @param array $response API response.
     * @return array|WP_Error
     */
    private function parse_send_response( $response ) {
        // Check for API-specific error indicators
        if ( isset( $response['error'] ) ) {
            return new WP_Error(
                'api_error',
                $response['error']['message'] ?? __( 'Unknown API error.', 'broodle-wa-connector' )
            );
        }

        if ( isset( $response['success'] ) && false === $response['success'] ) {
            // Check for detailed error in metaResponse
            $error_message = __( 'Failed to send message.', 'broodle-wa-connector' );

            if ( isset( $response['metaResponse']['error']['message'] ) ) {
                $error_message = $response['metaResponse']['error']['message'];

                // Add more details if available
                if ( isset( $response['metaResponse']['error']['error_data']['details'] ) ) {
                    $error_message .= ' - ' . $response['metaResponse']['error']['error_data']['details'];
                }
            } elseif ( isset( $response['message'] ) ) {
                $error_message = $response['message'];
            }

            return new WP_Error(
                'send_failed',
                $error_message
            );
        }

        // Check if the response indicates actual delivery status
        if ( isset( $response['success'] ) && true === $response['success'] ) {
            // Check for additional status information
            $status_message = '';
            if ( isset( $response['message'] ) ) {
                $status_message = $response['message'];
            }
            if ( isset( $response['status'] ) ) {
                $status_message .= ' (Status: ' . $response['status'] . ')';
            }
            if ( isset( $response['messageId'] ) ) {
                $status_message .= ' (ID: ' . $response['messageId'] . ')';
            }

            return array(
                'success' => true,
                'message_id' => $response['messageId'] ?? '',
                'status' => $response['status'] ?? 'sent',
                'status_message' => $status_message,
                'response_data' => $response,
            );
        }

        // If we get here, the response format is unexpected
        return new WP_Error(
            'unexpected_response',
            __( 'Unexpected API response format.', 'broodle-wa-connector' ) . ' Response: ' . wp_json_encode( $response )
        );
    }

    /**
     * Format phone number for WhatsApp
     *
     * @param string $phone_number Raw phone number.
     * @return string|WP_Error
     */
    private function format_phone_number( $phone_number ) {
        // Remove all non-digit characters except +
        $cleaned = preg_replace( '/[^\d+]/', '', $phone_number );

        if ( empty( $cleaned ) ) {
            return new WP_Error( 'invalid_phone', __( 'Invalid phone number format.', 'broodle-wa-connector' ) );
        }

        // If no country code, add default
        if ( 0 !== strpos( $cleaned, '+' ) ) {
            $default_country_code = Broodle_WA_Settings::get_setting( 'country_code', '+1' );
            $cleaned = $default_country_code . $cleaned;
        }

        // Validate format
        if ( ! preg_match( '/^\+\d{10,15}$/', $cleaned ) ) {
            return new WP_Error( 'invalid_phone_format', __( 'Phone number must be 10-15 digits with country code.', 'broodle-wa-connector' ) );
        }

        return $cleaned;
    }

    /**
     * Log API request for debugging
     *
     * @param string $url Request URL.
     * @param array  $args Request arguments.
     * @param int    $response_code Response code.
     * @param string $response_body Response body.
     */
    private function log_api_request( $url, $args, $response_code, $response_body ) {
        if ( ! defined( 'WP_DEBUG' ) || ! WP_DEBUG ) {
            return;
        }

        $log_data = array(
            'url' => $url,
            'method' => $args['method'],
            'headers' => $args['headers'],
            'body' => $args['body'],
            'response_code' => $response_code,
            'response_body' => $response_body,
            'timestamp' => current_time( 'mysql' ),
        );
    }

    /**
     * Get API status
     *
     * @return array
     */
    public function get_api_status() {
        $test_result = $this->test_connection();

        return array(
            'connected' => ! is_wp_error( $test_result ),
            'error' => is_wp_error( $test_result ) ? $test_result->get_error_message() : null,
            'last_checked' => current_time( 'mysql' ),
        );
    }

    /**
     * Validate template variables
     *
     * @param array $template_vars Template variables.
     * @return array Sanitized template variables.
     */
    public function validate_template_vars( $template_vars ) {
        if ( ! is_array( $template_vars ) ) {
            return array();
        }

        $sanitized = array();
        foreach ( $template_vars as $value ) {
            // Convert to string and sanitize
            $clean_value = sanitize_text_field( (string) $value );

            // CRITICAL FIX: Ensure no null or completely empty values that could cause API issues
            if ( $clean_value === null ) {
                $clean_value = '';
            }

            $sanitized[] = $clean_value;
        }

        // CRITICAL FIX: Ensure we don't send empty arrays that could cause default template values
        if ( empty( $sanitized ) ) {
            return array( '---' ); // Send at least one placeholder
        }

        return $sanitized;
    }

    /**
     * Get rate limit information
     *
     * @return array
     */
    public function get_rate_limit_info() {
        // This would typically come from API headers
        // For now, return default limits
        return array(
            'limit' => 1000, // Messages per hour
            'remaining' => 950,
            'reset_time' => time() + 3600,
        );
    }

    /**
     * Check if rate limited
     *
     * @return bool
     */
    public function is_rate_limited() {
        $rate_limit = $this->get_rate_limit_info();
        return $rate_limit['remaining'] <= 0;
    }
}
